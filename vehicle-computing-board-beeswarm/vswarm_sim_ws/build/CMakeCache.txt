# This is the CMakeCache file.
# For build in directory: /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a file.
ASSIMP_INCLUDE_DIRS:PATH=/usr/include

//Path to a library.
ASSIMP_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libassimp.so

//Builds the googlemock subproject
BUILD_GMOCK:BOOL=ON

//Build dynamically-linked binaries
BUILD_SHARED_LIBS:BOOL=ON

Boost_DATE_TIME_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/Boost-1.71.0

Boost_FILESYSTEM_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0

//Path to a file.
Boost_INCLUDE_DIR:PATH=/usr/include

Boost_IOSTREAMS_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_iostreams.so.1.71.0

Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0

Boost_REGEX_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0

Boost_SYSTEM_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0

Boost_THREAD_LIBRARY_RELEASE:STRING=/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0

//List of ';' separated packages to exclude
CATKIN_BLACKLIST_PACKAGES:STRING=

//catkin devel space
CATKIN_DEVEL_PREFIX:PATH=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel

//Catkin enable testing
CATKIN_ENABLE_TESTING:BOOL=ON

//Catkin skip testing
CATKIN_SKIP_TESTING:BOOL=OFF

//Replace the CMake install command with a custom implementation
// using symlinks instead of copying resources
CATKIN_SYMLINK_INSTALL:BOOL=OFF

//List of ';' separated packages to build
CATKIN_WHITELIST_PACKAGES:STRING=

//Path to a file.
CCD_INCLUDE_DIRS:PATH=/usr/include

//Path to a library.
CCD_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libccd.so

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=OFF

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/install

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=Project

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.10.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=10

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a file.
CPPZMQ_INCLUDE_DIRS:PATH=/usr/include

//Path to a file.
CURL_INCLUDE_DIR:PATH=/usr/include/x86_64-linux-gnu

//Path to a library.
CURL_LIBRARY_DEBUG:FILEPATH=CURL_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
CURL_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libcurl.so

//The directory containing a CMake configuration file for DART.
DART_DIR:PATH=/usr/share/dart/cmake

//Path to a file.
DL_INCLUDE_DIRS:PATH=/usr/include

//Path to a library.
DL_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libdl.so

//Path to a program.
DOXYGEN_EXECUTABLE:FILEPATH=DOXYGEN_EXECUTABLE-NOTFOUND

//Path to a program.
EMPY_EXECUTABLE:FILEPATH=EMPY_EXECUTABLE-NOTFOUND

//Empy script
EMPY_SCRIPT:STRING=/home/<USER>/anaconda3/lib/python3.9/site-packages/em.py

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=/usr/lib/cmake/eigen3

//Path to a file.
FCL_INCLUDE_DIRS:PATH=/usr/include

//Path to a library.
FCL_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libfcl.so

//The directory containing a CMake configuration file for GMock.
GMock_DIR:PATH=GMock_DIR-NOTFOUND

//The directory containing a CMake configuration file for GTest.
GTest_DIR:PATH=/home/<USER>/anaconda3/lib/cmake/GTest

//Compile with a sanitizer. Options are: Address, Memory, MemoryWithOrigins,
// Undefined, Thread, Leak, 'Address;Undefined', CFI
GZ_SANITIZER:STRING=

//Compile with a sanitizer. Options are: Address, Memory, MemoryWithOrigins,
// Undefined, Thread, Leak, 'Address;Undefined', CFI
IGN_SANITIZER:STRING=

//Enable installation of googletest. (Projects embedding googletest
// may want to turn this OFF.)
INSTALL_GTEST:BOOL=OFF

//Path to a library.
JSONCPP_LIBRARY_jsoncpp:FILEPATH=/usr/lib/x86_64-linux-gnu/libjsoncpp.so

//lsb_release executable was found
LSB_FOUND:BOOL=TRUE

//Path to a program.
LSB_RELEASE_EXECUTABLE:FILEPATH=/usr/bin/lsb_release

//Path to a program.
NOSETESTS:FILEPATH=/usr/bin/nosetests3

//x
OGRE_CONFIG_INCLUDE_DIR:PATH=/usr/include/OGRE

//x
OGRE_INCLUDE_DIR:PATH=/usr/include/OGRE

//x
OGRE_LIBRARY_DBG:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreMain.so

//x
OGRE_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_LIBRARY_REL:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreMain.so

//x
OGRE_MEDIA_DIR:PATH=OGRE_MEDIA_DIR-NOTFOUND

//x
OGRE_Overlay_INCLUDE_DIR:PATH=/usr/include/OGRE/Overlay

//x
OGRE_Overlay_LIBRARY_DBG:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreOverlay.so

//x
OGRE_Overlay_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Overlay_LIBRARY_REL:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreOverlay.so

//Ogre plugin dir (debug)
OGRE_PLUGIN_DIR_DBG:STRING=

//Ogre plugin dir (release)
OGRE_PLUGIN_DIR_REL:STRING=

//x
OGRE_Paging_INCLUDE_DIR:PATH=/usr/include/OGRE/Paging

//x
OGRE_Paging_LIBRARY_DBG:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgrePaging.so

//x
OGRE_Paging_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Paging_LIBRARY_REL:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgrePaging.so

//x
OGRE_Plugin_BSPSceneManager_INCLUDE_DIR:PATH=/usr/include/OGRE/Plugins/BSPSceneManager

//x
OGRE_Plugin_BSPSceneManager_LIBRARY_DBG:FILEPATH=OGRE_Plugin_BSPSceneManager_LIBRARY_DBG-NOTFOUND

//x
OGRE_Plugin_BSPSceneManager_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Plugin_BSPSceneManager_LIBRARY_REL:FILEPATH=OGRE_Plugin_BSPSceneManager_LIBRARY_REL-NOTFOUND

//x
OGRE_Plugin_CgProgramManager_INCLUDE_DIR:PATH=OGRE_Plugin_CgProgramManager_INCLUDE_DIR-NOTFOUND

//x
OGRE_Plugin_CgProgramManager_LIBRARY_DBG:FILEPATH=OGRE_Plugin_CgProgramManager_LIBRARY_DBG-NOTFOUND

//x
OGRE_Plugin_CgProgramManager_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Plugin_CgProgramManager_LIBRARY_REL:FILEPATH=OGRE_Plugin_CgProgramManager_LIBRARY_REL-NOTFOUND

//x
OGRE_Plugin_OctreeSceneManager_INCLUDE_DIR:PATH=/usr/include/OGRE/Plugins/OctreeSceneManager

//x
OGRE_Plugin_OctreeSceneManager_LIBRARY_DBG:FILEPATH=OGRE_Plugin_OctreeSceneManager_LIBRARY_DBG-NOTFOUND

//x
OGRE_Plugin_OctreeSceneManager_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Plugin_OctreeSceneManager_LIBRARY_REL:FILEPATH=OGRE_Plugin_OctreeSceneManager_LIBRARY_REL-NOTFOUND

//x
OGRE_Plugin_OctreeZone_INCLUDE_DIR:PATH=/usr/include/OGRE/Plugins/OctreeZone

//x
OGRE_Plugin_OctreeZone_LIBRARY_DBG:FILEPATH=OGRE_Plugin_OctreeZone_LIBRARY_DBG-NOTFOUND

//x
OGRE_Plugin_OctreeZone_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Plugin_OctreeZone_LIBRARY_REL:FILEPATH=OGRE_Plugin_OctreeZone_LIBRARY_REL-NOTFOUND

//x
OGRE_Plugin_PCZSceneManager_INCLUDE_DIR:PATH=/usr/include/OGRE/Plugins/PCZSceneManager

//x
OGRE_Plugin_PCZSceneManager_LIBRARY_DBG:FILEPATH=OGRE_Plugin_PCZSceneManager_LIBRARY_DBG-NOTFOUND

//x
OGRE_Plugin_PCZSceneManager_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Plugin_PCZSceneManager_LIBRARY_REL:FILEPATH=OGRE_Plugin_PCZSceneManager_LIBRARY_REL-NOTFOUND

//x
OGRE_Plugin_ParticleFX_INCLUDE_DIR:PATH=/usr/include/OGRE/Plugins/ParticleFX

//x
OGRE_Plugin_ParticleFX_LIBRARY_DBG:FILEPATH=OGRE_Plugin_ParticleFX_LIBRARY_DBG-NOTFOUND

//x
OGRE_Plugin_ParticleFX_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Plugin_ParticleFX_LIBRARY_REL:FILEPATH=OGRE_Plugin_ParticleFX_LIBRARY_REL-NOTFOUND

//Path to a file.
OGRE_Property_INCLUDE_DIR:PATH=/usr/include/OGRE/Property

//Path to a library.
OGRE_Property_LIBRARY_DBG:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreProperty.so

//Path to a library.
OGRE_Property_LIBRARY_REL:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreProperty.so

//Path to a file.
OGRE_RTShaderSystem_INCLUDE_DIR:PATH=/usr/include/OGRE/RTShaderSystem

//Path to a library.
OGRE_RTShaderSystem_LIBRARY_DBG:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreRTShaderSystem.so

//Path to a library.
OGRE_RTShaderSystem_LIBRARY_REL:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreRTShaderSystem.so

//x
OGRE_RenderSystem_Direct3D11_INCLUDE_DIR:PATH=OGRE_RenderSystem_Direct3D11_INCLUDE_DIR-NOTFOUND

//x
OGRE_RenderSystem_Direct3D11_LIBRARY_DBG:FILEPATH=OGRE_RenderSystem_Direct3D11_LIBRARY_DBG-NOTFOUND

//x
OGRE_RenderSystem_Direct3D11_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_RenderSystem_Direct3D11_LIBRARY_REL:FILEPATH=OGRE_RenderSystem_Direct3D11_LIBRARY_REL-NOTFOUND

//x
OGRE_RenderSystem_Direct3D9_INCLUDE_DIR:PATH=OGRE_RenderSystem_Direct3D9_INCLUDE_DIR-NOTFOUND

//x
OGRE_RenderSystem_Direct3D9_LIBRARY_DBG:FILEPATH=OGRE_RenderSystem_Direct3D9_LIBRARY_DBG-NOTFOUND

//x
OGRE_RenderSystem_Direct3D9_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_RenderSystem_Direct3D9_LIBRARY_REL:FILEPATH=OGRE_RenderSystem_Direct3D9_LIBRARY_REL-NOTFOUND

//x
OGRE_RenderSystem_GL3Plus_INCLUDE_DIR:PATH=OGRE_RenderSystem_GL3Plus_INCLUDE_DIR-NOTFOUND

//x
OGRE_RenderSystem_GL3Plus_LIBRARY_DBG:FILEPATH=OGRE_RenderSystem_GL3Plus_LIBRARY_DBG-NOTFOUND

//x
OGRE_RenderSystem_GL3Plus_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_RenderSystem_GL3Plus_LIBRARY_REL:FILEPATH=OGRE_RenderSystem_GL3Plus_LIBRARY_REL-NOTFOUND

//x
OGRE_RenderSystem_GLES2_INCLUDE_DIR:PATH=/usr/include/OGRE/RenderSystems/GLES2

//x
OGRE_RenderSystem_GLES2_LIBRARY_DBG:FILEPATH=OGRE_RenderSystem_GLES2_LIBRARY_DBG-NOTFOUND

//x
OGRE_RenderSystem_GLES2_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_RenderSystem_GLES2_LIBRARY_REL:FILEPATH=OGRE_RenderSystem_GLES2_LIBRARY_REL-NOTFOUND

//x
OGRE_RenderSystem_GLES_INCLUDE_DIR:PATH=OGRE_RenderSystem_GLES_INCLUDE_DIR-NOTFOUND

//x
OGRE_RenderSystem_GLES_LIBRARY_DBG:FILEPATH=OGRE_RenderSystem_GLES_LIBRARY_DBG-NOTFOUND

//x
OGRE_RenderSystem_GLES_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_RenderSystem_GLES_LIBRARY_REL:FILEPATH=OGRE_RenderSystem_GLES_LIBRARY_REL-NOTFOUND

//x
OGRE_RenderSystem_GL_INCLUDE_DIR:PATH=/usr/include/OGRE/RenderSystems/GL

//x
OGRE_RenderSystem_GL_LIBRARY_DBG:FILEPATH=OGRE_RenderSystem_GL_LIBRARY_DBG-NOTFOUND

//x
OGRE_RenderSystem_GL_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_RenderSystem_GL_LIBRARY_REL:FILEPATH=OGRE_RenderSystem_GL_LIBRARY_REL-NOTFOUND

//x
OGRE_Terrain_INCLUDE_DIR:PATH=/usr/include/OGRE/Terrain

//x
OGRE_Terrain_LIBRARY_DBG:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreTerrain.so

//x
OGRE_Terrain_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Terrain_LIBRARY_REL:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreTerrain.so

//x
OGRE_Volume_INCLUDE_DIR:PATH=/usr/include/OGRE/Volume

//x
OGRE_Volume_LIBRARY_DBG:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreVolume.so

//x
OGRE_Volume_LIBRARY_FWK:STRING=NOTFOUND

//x
OGRE_Volume_LIBRARY_REL:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreVolume.so

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a program.
PYTHON_EXECUTABLE:FILEPATH=/home/<USER>/anaconda3/bin/python3

//Specify specific Python version to use ('major.minor' or 'major')
PYTHON_VERSION:STRING=3

//Location of Python module em
PY_EM:STRING=/home/<USER>/anaconda3/lib/python3.9/site-packages/em.py

//Value Computed by CMake
Project_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build

//Value Computed by CMake
Project_SOURCE_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src

//The directory containing a CMake configuration file for Protobuf.
Protobuf_DIR:PATH=Protobuf_DIR-NOTFOUND

//Path to a file.
Protobuf_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
Protobuf_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotobuf.so

//Path to a library.
Protobuf_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotobuf.so

//Path to a library.
Protobuf_LITE_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotobuf-lite.so

//Path to a library.
Protobuf_LITE_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotobuf-lite.so

//The Google Protocol Buffers Compiler
Protobuf_PROTOC_EXECUTABLE:FILEPATH=/home/<USER>/anaconda3/bin/protoc

//Path to a library.
Protobuf_PROTOC_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotoc.so

//Path to a library.
Protobuf_PROTOC_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libprotoc.so

//Path to a library.
RT_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/librt.so

//Enable debian style python package layout
SETUPTOOLS_DEB_LAYOUT:BOOL=ON

//Name of the computer/site where compile is being run
SITE:STRING=VSWARM11

//The directory containing a CMake configuration file for Simbody.
Simbody_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/simbody

//Path to a library.
TINYXML2_LIBRARY_/usr/lib/x86_64-linux-gnu/libtinyxml2.so:FILEPATH=TINYXML2_LIBRARY_/usr/lib/x86_64-linux-gnu/libtinyxml2.so-NOTFOUND

//Path to a library.
TINYXML2_LIBRARY_tinyxml2:FILEPATH=/usr/lib/x86_64-linux-gnu/libtinyxml2.so

//LSB Distrib tag
UBUNTU:BOOL=TRUE

//LSB Distrib - codename tag
UBUNTU_FOCAL:BOOL=TRUE

//Path to a library.
UUID_LIBRARY_/usr/lib/x86_64-linux-gnu/libuuid.so:FILEPATH=UUID_LIBRARY_/usr/lib/x86_64-linux-gnu/libuuid.so-NOTFOUND

//Path to a library.
UUID_LIBRARY_uuid:FILEPATH=/usr/lib/x86_64-linux-gnu/libuuid.so

//Path to a library.
YAML_LIBRARY_yaml:FILEPATH=/usr/lib/x86_64-linux-gnu/libyaml.so

//Path to a library.
ZIP_LIBRARY_zip:FILEPATH=/usr/lib/x86_64-linux-gnu/libzip.so

//The directory containing a CMake configuration file for ZeroMQ.
ZeroMQ_DIR:PATH=/home/<USER>/anaconda3/lib/cmake/ZeroMQ

//Path to a file.
_gmock_INCLUDES:FILEPATH=/usr/src/googletest/googlemock/include/gmock/gmock.h

//Path to a file.
_gmock_SOURCES:FILEPATH=/usr/src/gmock/src/gmock.cc

//Path to a file.
_gtest_INCLUDES:FILEPATH=/usr/include/gtest/gtest.h

//Path to a file.
_gtest_SOURCES:FILEPATH=/usr/src/gtest/src/gtest.cc

//The directory containing a CMake configuration file for actionlib.
actionlib_DIR:PATH=/opt/ros/noetic/share/actionlib/cmake

//The directory containing a CMake configuration file for actionlib_msgs.
actionlib_msgs_DIR:PATH=/opt/ros/noetic/share/actionlib_msgs/cmake

//The directory containing a CMake configuration file for angles.
angles_DIR:PATH=/opt/ros/noetic/share/angles/cmake

//The directory containing a CMake configuration file for boost_atomic.
boost_atomic_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_atomic-1.71.0

//The directory containing a CMake configuration file for boost_date_time.
boost_date_time_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_date_time-1.71.0

//The directory containing a CMake configuration file for boost_filesystem.
boost_filesystem_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_filesystem-1.71.0

//The directory containing a CMake configuration file for boost_headers.
boost_headers_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_headers-1.71.0

//The directory containing a CMake configuration file for boost_iostreams.
boost_iostreams_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_iostreams-1.71.0

//The directory containing a CMake configuration file for boost_program_options.
boost_program_options_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_program_options-1.71.0

//The directory containing a CMake configuration file for boost_regex.
boost_regex_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_regex-1.71.0

//The directory containing a CMake configuration file for boost_system.
boost_system_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_system-1.71.0

//The directory containing a CMake configuration file for boost_thread.
boost_thread_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/boost_thread-1.71.0

//The directory containing a CMake configuration file for catkin.
catkin_DIR:PATH=/opt/ros/noetic/share/catkin/cmake

//The directory containing a CMake configuration file for ccd.
ccd_DIR:PATH=ccd_DIR-NOTFOUND

//The directory containing a CMake configuration file for class_loader.
class_loader_DIR:PATH=/opt/ros/noetic/share/class_loader/cmake

//The directory containing a CMake configuration file for control_msgs.
control_msgs_DIR:PATH=/opt/ros/noetic/share/control_msgs/cmake

//The directory containing a CMake configuration file for control_toolbox.
control_toolbox_DIR:PATH=/opt/ros/noetic/share/control_toolbox/cmake

//The directory containing a CMake configuration file for controller_interface.
controller_interface_DIR:PATH=/opt/ros/noetic/share/controller_interface/cmake

//The directory containing a CMake configuration file for controller_manager.
controller_manager_DIR:PATH=/opt/ros/noetic/share/controller_manager/cmake

//The directory containing a CMake configuration file for controller_manager_msgs.
controller_manager_msgs_DIR:PATH=/opt/ros/noetic/share/controller_manager_msgs/cmake

//The directory containing a CMake configuration file for cpp_common.
cpp_common_DIR:PATH=/opt/ros/noetic/share/cpp_common/cmake

//Value Computed by CMake
cpp_version_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/cpp_version

//Value Computed by CMake
cpp_version_SOURCE_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/cpp_version

//The directory containing a CMake configuration file for cv_bridge.
cv_bridge_DIR:PATH=/opt/ros/noetic/share/cv_bridge/cmake

//The directory containing a CMake configuration file for dynamic_reconfigure.
dynamic_reconfigure_DIR:PATH=/opt/ros/noetic/share/dynamic_reconfigure/cmake

//The directory containing a CMake configuration file for gazebo.
gazebo_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/gazebo

//The directory containing a CMake configuration file for gazebo_msgs.
gazebo_msgs_DIR:PATH=/opt/ros/noetic/share/gazebo_msgs/cmake

//Path to a library.
gazebo_proto_msgs_lib:FILEPATH=/usr/lib/x86_64-linux-gnu/libgazebo_msgs.so

//The directory containing a CMake configuration file for gazebo_ros.
gazebo_ros_DIR:PATH=/opt/ros/noetic/share/gazebo_ros/cmake

//The directory containing a CMake configuration file for gazebo_ros_control.
gazebo_ros_control_DIR:PATH=/opt/ros/noetic/share/gazebo_ros_control/cmake

//The directory containing a CMake configuration file for gencpp.
gencpp_DIR:PATH=/opt/ros/noetic/share/gencpp/cmake

//The directory containing a CMake configuration file for geneus.
geneus_DIR:PATH=/opt/ros/noetic/share/geneus/cmake

//The directory containing a CMake configuration file for genlisp.
genlisp_DIR:PATH=/opt/ros/noetic/share/genlisp/cmake

//The directory containing a CMake configuration file for genmsg.
genmsg_DIR:PATH=/opt/ros/noetic/share/genmsg/cmake

//The directory containing a CMake configuration file for gennodejs.
gennodejs_DIR:PATH=/opt/ros/noetic/share/gennodejs/cmake

//The directory containing a CMake configuration file for genpy.
genpy_DIR:PATH=/opt/ros/noetic/share/genpy/cmake

//The directory containing a CMake configuration file for geometry_msgs.
geometry_msgs_DIR:PATH=/opt/ros/noetic/share/geometry_msgs/cmake

//Value Computed by CMake
gmock_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/gtest/googlemock

//Dependencies for the target
gmock_LIB_DEPENDS:STATIC=general;gtest;

//Value Computed by CMake
gmock_SOURCE_DIR:STATIC=/usr/src/googletest/googlemock

//Build all of Google Mock's own tests.
gmock_build_tests:BOOL=OFF

//Dependencies for the target
gmock_main_LIB_DEPENDS:STATIC=general;gmock;

//Value Computed by CMake
googletest-distribution_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/gtest

//Value Computed by CMake
googletest-distribution_SOURCE_DIR:STATIC=/usr/src/googletest

//Value Computed by CMake
gtest_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/gtest/googletest

//Value Computed by CMake
gtest_SOURCE_DIR:STATIC=/usr/src/googletest/googletest

//Build gtest's sample programs.
gtest_build_samples:BOOL=OFF

//Build all of gtest's own tests.
gtest_build_tests:BOOL=OFF

//Disable uses of pthreads in gtest.
gtest_disable_pthreads:BOOL=OFF

//Use shared (DLL) run-time lib even when Google Test is built
// as static lib.
gtest_force_shared_crt:BOOL=OFF

//Build gtest with internal symbols hidden in shared libraries.
gtest_hide_internal_symbols:BOOL=OFF

//Dependencies for the target
gtest_main_LIB_DEPENDS:STATIC=general;gtest;

//The directory containing a CMake configuration file for hardware_interface.
hardware_interface_DIR:PATH=/opt/ros/noetic/share/hardware_interface/cmake

//The directory containing a CMake configuration file for ignition-cmake2.
ignition-cmake2_DIR:PATH=/usr/share/cmake/ignition-cmake2

//The directory containing a CMake configuration file for ignition-common3-graphics.
ignition-common3-graphics_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/ignition-common3-graphics

//The directory containing a CMake configuration file for ignition-common3.
ignition-common3_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/ignition-common3

//The directory containing a CMake configuration file for ignition-fuel_tools4.
ignition-fuel_tools4_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/ignition-fuel_tools4

//The directory containing a CMake configuration file for ignition-math6.
ignition-math6_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/ignition-math6

//The directory containing a CMake configuration file for ignition-msgs5.
ignition-msgs5_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/ignition-msgs5

//The directory containing a CMake configuration file for ignition-transport8.
ignition-transport8_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/ignition-transport8

//The directory containing a CMake configuration file for joint_limits_interface.
joint_limits_interface_DIR:PATH=/opt/ros/noetic/share/joint_limits_interface/cmake

//The directory containing a CMake configuration file for jsoncpp.
jsoncpp_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/jsoncpp

//Value Computed by CMake
led_gazebo_plugin_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/led_gazebo_plugin

//Dependencies for the target
led_gazebo_plugin_LIB_DEPENDS:STATIC=general;/opt/ros/noetic/lib/libgazebo_ros_api_plugin.so;general;/opt/ros/noetic/lib/libgazebo_ros_paths_plugin.so;general;/usr/lib/x86_64-linux-gnu/libtinyxml.so;general;/opt/ros/noetic/lib/libroslib.so;general;/opt/ros/noetic/lib/librospack.so;general;/usr/lib/x86_64-linux-gnu/libpython3.8.so;general;/usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libtinyxml2.so;general;/opt/ros/noetic/lib/libtf.so;general;/opt/ros/noetic/lib/libtf2_ros.so;general;/opt/ros/noetic/lib/libactionlib.so;general;/opt/ros/noetic/lib/libmessage_filters.so;general;/opt/ros/noetic/lib/libroscpp.so;general;/usr/lib/x86_64-linux-gnu/libpthread.so;general;/usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0;general;/opt/ros/noetic/lib/libxmlrpcpp.so;general;/opt/ros/noetic/lib/libtf2.so;general;/opt/ros/noetic/lib/librosconsole.so;general;/opt/ros/noetic/lib/librosconsole_log4cxx.so;general;/opt/ros/noetic/lib/librosconsole_backend_interface.so;general;/usr/lib/x86_64-linux-gnu/liblog4cxx.so;general;/usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0;general;/opt/ros/noetic/lib/libdynamic_reconfigure_config_init_mutex.so;general;/opt/ros/noetic/lib/libroscpp_serialization.so;general;/opt/ros/noetic/lib/librostime.so;general;/usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0;general;/opt/ros/noetic/lib/libcpp_common.so;general;/usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0;general;/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4;general;BulletSoftBody;general;BulletDynamics;general;BulletCollision;general;LinearMath;general;SimTKcommon;general;SimTKmath;general;SimTKsimbody;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;dart;general;/usr/lib/x86_64-linux-gnu/libgazebo.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_client.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_gui.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_sensors.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_rendering.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_physics.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_ode.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_transport.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_msgs.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_util.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_common.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_gimpact.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_opcode.so;general;/usr/lib/x86_64-linux-gnu/libgazebo_opende_ou.so;general;Boost::thread;general;Boost::system;general;Boost::filesystem;general;Boost::program_options;general;Boost::regex;general;Boost::iostreams;general;Boost::date_time;general;/usr/lib/x86_64-linux-gnu/libprotobuf.so;general;-lpthread;general;sdformat9::sdformat9;optimized;/usr/lib/x86_64-linux-gnu/libOgreMain.so;debug;/usr/lib/x86_64-linux-gnu/libOgreMain.so;general;Boost::thread;general;Boost::date_time;optimized;/usr/lib/x86_64-linux-gnu/libOgreTerrain.so;debug;/usr/lib/x86_64-linux-gnu/libOgreTerrain.so;optimized;/usr/lib/x86_64-linux-gnu/libOgrePaging.so;debug;/usr/lib/x86_64-linux-gnu/libOgrePaging.so;general;ignition-common3::ignition-common3-graphics;

//Value Computed by CMake
led_gazebo_plugin_SOURCE_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/led_gazebo_plugin

//Path to a library.
lib:FILEPATH=/opt/ros/noetic/lib/libtf2.so

//The directory containing a CMake configuration file for message_filters.
message_filters_DIR:PATH=/opt/ros/noetic/share/message_filters/cmake

//The directory containing a CMake configuration file for message_generation.
message_generation_DIR:PATH=/opt/ros/noetic/share/message_generation/cmake

//The directory containing a CMake configuration file for message_runtime.
message_runtime_DIR:PATH=/opt/ros/noetic/share/message_runtime/cmake

//The directory containing a CMake configuration file for nav_msgs.
nav_msgs_DIR:PATH=/opt/ros/noetic/share/nav_msgs/cmake

//The directory containing a CMake configuration file for nlohmann_json.
nlohmann_json_DIR:PATH=/usr/lib/cmake/nlohmann_json

//The directory containing a CMake configuration file for octomap.
octomap_DIR:PATH=/usr/lib/x86_64-linux-gnu/octomap

//Path to a library.
onelib:FILEPATH=/usr/lib/x86_64-linux-gnu/libgazebo_opende_ou.so

//Path to a library.
pkgcfg_lib_BULLET_BulletCollision:FILEPATH=/usr/lib/x86_64-linux-gnu/libBulletCollision.so

//Path to a library.
pkgcfg_lib_BULLET_BulletDynamics:FILEPATH=/usr/lib/x86_64-linux-gnu/libBulletDynamics.so

//Path to a library.
pkgcfg_lib_BULLET_BulletSoftBody:FILEPATH=/usr/lib/x86_64-linux-gnu/libBulletSoftBody.so

//Path to a library.
pkgcfg_lib_BULLET_LinearMath:FILEPATH=/usr/lib/x86_64-linux-gnu/libLinearMath.so

//Path to a library.
pkgcfg_lib_JSONCPP_jsoncpp:FILEPATH=/usr/lib/x86_64-linux-gnu/libjsoncpp.so

//Path to a library.
pkgcfg_lib_MOSQUITTO_mosquitto:FILEPATH=/usr/lib/x86_64-linux-gnu/libmosquitto.so

//Path to a library.
pkgcfg_lib_OGRE_PKGC_OgreMain:FILEPATH=/usr/lib/x86_64-linux-gnu/libOgreMain.so

//Path to a library.
pkgcfg_lib_OGRE_PKGC_pthread:FILEPATH=/usr/lib/x86_64-linux-gnu/libpthread.so

//Path to a library.
pkgcfg_lib_PC_ASSIMP_assimp:FILEPATH=/usr/lib/x86_64-linux-gnu/libassimp.so

//Path to a library.
pkgcfg_lib_PC_CCD_ccd:FILEPATH=/usr/lib/x86_64-linux-gnu/libccd.so

//Path to a library.
pkgcfg_lib_PC_CCD_m:FILEPATH=/usr/lib/x86_64-linux-gnu/libm.so

//Path to a library.
pkgcfg_lib_PC_CURL_curl:FILEPATH=/usr/lib/x86_64-linux-gnu/libcurl.so

//Path to a library.
pkgcfg_lib_PC_FCL_fcl:FILEPATH=/usr/lib/x86_64-linux-gnu/libfcl.so

//Path to a library.
pkgcfg_lib_TINYXML2_tinyxml2:FILEPATH=/usr/lib/x86_64-linux-gnu/libtinyxml2.so

//Path to a library.
pkgcfg_lib_UUID_uuid:FILEPATH=/usr/lib/x86_64-linux-gnu/libuuid.so

//Path to a library.
pkgcfg_lib_YAML_yaml:FILEPATH=/usr/lib/x86_64-linux-gnu/libyaml.so

//Path to a library.
pkgcfg_lib_ZIP_zip:FILEPATH=/usr/lib/x86_64-linux-gnu/libzip.so

//The directory containing a CMake configuration file for pluginlib.
pluginlib_DIR:PATH=/opt/ros/noetic/share/pluginlib/cmake

//The directory containing a CMake configuration file for realtime_tools.
realtime_tools_DIR:PATH=/opt/ros/noetic/share/realtime_tools/cmake

//The directory containing a CMake configuration file for rosbag.
rosbag_DIR:PATH=/opt/ros/noetic/share/rosbag/cmake

//The directory containing a CMake configuration file for rosbag_storage.
rosbag_storage_DIR:PATH=/opt/ros/noetic/share/rosbag_storage/cmake

//The directory containing a CMake configuration file for rosconsole.
rosconsole_DIR:PATH=/opt/ros/noetic/share/rosconsole/cmake

//The directory containing a CMake configuration file for rosconsole_bridge.
rosconsole_bridge_DIR:PATH=/opt/ros/noetic/share/rosconsole_bridge/cmake

//The directory containing a CMake configuration file for roscpp.
roscpp_DIR:PATH=/opt/ros/noetic/share/roscpp/cmake

//The directory containing a CMake configuration file for roscpp_serialization.
roscpp_serialization_DIR:PATH=/opt/ros/noetic/share/roscpp_serialization/cmake

//The directory containing a CMake configuration file for roscpp_traits.
roscpp_traits_DIR:PATH=/opt/ros/noetic/share/roscpp_traits/cmake

//The directory containing a CMake configuration file for rosgraph.
rosgraph_DIR:PATH=/opt/ros/noetic/share/rosgraph/cmake

//The directory containing a CMake configuration file for rosgraph_msgs.
rosgraph_msgs_DIR:PATH=/opt/ros/noetic/share/rosgraph_msgs/cmake

//The directory containing a CMake configuration file for roslib.
roslib_DIR:PATH=/opt/ros/noetic/share/roslib/cmake

//The directory containing a CMake configuration file for roslz4.
roslz4_DIR:PATH=/opt/ros/noetic/share/roslz4/cmake

//The directory containing a CMake configuration file for rospack.
rospack_DIR:PATH=/opt/ros/noetic/share/rospack/cmake

//The directory containing a CMake configuration file for rospy.
rospy_DIR:PATH=/opt/ros/noetic/share/rospy/cmake

//The directory containing a CMake configuration file for rostime.
rostime_DIR:PATH=/opt/ros/noetic/share/rostime/cmake

//The directory containing a CMake configuration file for sdformat9.
sdformat9_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/sdformat9

//The directory containing a CMake configuration file for sensor_msgs.
sensor_msgs_DIR:PATH=/opt/ros/noetic/share/sensor_msgs/cmake

//The directory containing a CMake configuration file for std_msgs.
std_msgs_DIR:PATH=/opt/ros/noetic/share/std_msgs/cmake

//The directory containing a CMake configuration file for std_srvs.
std_srvs_DIR:PATH=/opt/ros/noetic/share/std_srvs/cmake

//Value Computed by CMake
swarm_control_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/swarm_control

//Value Computed by CMake
swarm_control_SOURCE_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_control

//Value Computed by CMake
swarm_experiment_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/swarm_experiment

//The directory containing a CMake configuration file for swarm_experiment.
swarm_experiment_DIR:PATH=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel/share/swarm_experiment/cmake

//Value Computed by CMake
swarm_experiment_SOURCE_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/swarm_experiment

//The directory containing a CMake configuration file for tf2.
tf2_DIR:PATH=/opt/ros/noetic/share/tf2/cmake

//The directory containing a CMake configuration file for tf2_msgs.
tf2_msgs_DIR:PATH=/opt/ros/noetic/share/tf2_msgs/cmake

//The directory containing a CMake configuration file for tf2_py.
tf2_py_DIR:PATH=/opt/ros/noetic/share/tf2_py/cmake

//The directory containing a CMake configuration file for tf2_ros.
tf2_ros_DIR:PATH=/opt/ros/noetic/share/tf2_ros/cmake

//The directory containing a CMake configuration file for tf.
tf_DIR:PATH=/opt/ros/noetic/share/tf/cmake

//The directory containing a CMake configuration file for topic_tools.
topic_tools_DIR:PATH=/opt/ros/noetic/share/topic_tools/cmake

//The directory containing a CMake configuration file for trajectory_msgs.
trajectory_msgs_DIR:PATH=/opt/ros/noetic/share/trajectory_msgs/cmake

//The directory containing a CMake configuration file for transmission_interface.
transmission_interface_DIR:PATH=/opt/ros/noetic/share/transmission_interface/cmake

//The directory containing a CMake configuration file for urdf.
urdf_DIR:PATH=/opt/ros/noetic/share/urdf/cmake

//Value Computed by CMake
visual_swarm_integration_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/visual_swarm_integration

//Value Computed by CMake
visual_swarm_integration_SOURCE_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/visual_swarm_integration

//Value Computed by CMake
vswarm_sim_BINARY_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/vswarm_sim

//The directory containing a CMake configuration file for vswarm_sim.
vswarm_sim_DIR:PATH=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel/share/vswarm_sim/cmake

//Value Computed by CMake
vswarm_sim_SOURCE_DIR:STATIC=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/vswarm_sim

//The directory containing a CMake configuration file for xacro.
xacro_DIR:PATH=/opt/ros/noetic/share/xacro/cmake

//The directory containing a CMake configuration file for xmlrpcpp.
xmlrpcpp_DIR:PATH=/opt/ros/noetic/share/xmlrpcpp/cmake

//The directory containing a CMake configuration file for yaml.
yaml_DIR:PATH=yaml_DIR-NOTFOUND


########################
# INTERNAL cache entries
########################

BULLET_CFLAGS:INTERNAL=-I/usr/include/bullet
BULLET_CFLAGS_I:INTERNAL=
BULLET_CFLAGS_OTHER:INTERNAL=
BULLET_FOUND:INTERNAL=1
BULLET_INCLUDEDIR:INTERNAL=
BULLET_INCLUDE_DIRS:INTERNAL=/usr/include/bullet
BULLET_LDFLAGS:INTERNAL=-lBulletSoftBody;-lBulletDynamics;-lBulletCollision;-lLinearMath
BULLET_LDFLAGS_OTHER:INTERNAL=
BULLET_LIBDIR:INTERNAL=
BULLET_LIBRARIES:INTERNAL=BulletSoftBody;BulletDynamics;BulletCollision;LinearMath
BULLET_LIBRARY_DIRS:INTERNAL=
BULLET_LIBS:INTERNAL=
BULLET_LIBS_L:INTERNAL=
BULLET_LIBS_OTHER:INTERNAL=
BULLET_LIBS_PATHS:INTERNAL=
BULLET_MODULE_NAME:INTERNAL=bullet
BULLET_PREFIX:INTERNAL=
BULLET_STATIC_CFLAGS:INTERNAL=-I/usr/include/bullet
BULLET_STATIC_CFLAGS_I:INTERNAL=
BULLET_STATIC_CFLAGS_OTHER:INTERNAL=
BULLET_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/bullet
BULLET_STATIC_LDFLAGS:INTERNAL=-lBulletSoftBody;-lBulletDynamics;-lBulletCollision;-lLinearMath
BULLET_STATIC_LDFLAGS_OTHER:INTERNAL=
BULLET_STATIC_LIBDIR:INTERNAL=
BULLET_STATIC_LIBRARIES:INTERNAL=BulletSoftBody;BulletDynamics;BulletCollision;LinearMath
BULLET_STATIC_LIBRARY_DIRS:INTERNAL=
BULLET_STATIC_LIBS:INTERNAL=
BULLET_STATIC_LIBS_L:INTERNAL=
BULLET_STATIC_LIBS_OTHER:INTERNAL=
BULLET_STATIC_LIBS_PATHS:INTERNAL=
BULLET_VERSION:INTERNAL=2.88
BULLET_bullet_INCLUDEDIR:INTERNAL=
BULLET_bullet_LIBDIR:INTERNAL=
BULLET_bullet_PREFIX:INTERNAL=
BULLET_bullet_VERSION:INTERNAL=
//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//catkin environment
CATKIN_ENV:INTERNAL=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/catkin_generated/env_cached.sh
CATKIN_TEST_RESULTS_DIR:INTERNAL=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/test_results
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=16
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=10
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.16
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPPZMQ_INCLUDE_DIRS
CPPZMQ_INCLUDE_DIRS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_INCLUDE_DIR
CURL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_DEBUG
CURL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_RELEASE
CURL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
DART_DEFINITIONS:INTERNAL=
DART_INCLUDE_DIRS:INTERNAL=/usr/include
DART_LIBRARIES:INTERNAL=dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart
//Boost min version requirement
DART_MIN_BOOST_VERSION:INTERNAL=1.58.0
DART_dart_DEFINITIONS:INTERNAL=
DART_dart_DEPENDENCIES:INTERNAL=external-odelcpsolver
DART_dart_FOUND:INTERNAL=TRUE
DART_dart_INCLUDE_DIRS:INTERNAL=
DART_dart_LIBRARIES:INTERNAL=dart
//ADVANCED property for variable: DL_INCLUDE_DIRS
DL_INCLUDE_DIRS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: DL_LIBRARIES
DL_LIBRARIES-ADVANCED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[/usr/lib/x86_64-linux-gnu/cmake/Boost-1.71.0/BoostConfig.cmake][cfound components: thread system filesystem program_options regex iostreams date_time ][v1.71.0(1.40.0)]
//Details about finding CURL
FIND_PACKAGE_MESSAGE_DETAILS_CURL:INTERNAL=[/usr/lib/x86_64-linux-gnu/libcurl.so][/usr/include/x86_64-linux-gnu][c ][v7.68.0()]
//Details about finding DART
FIND_PACKAGE_MESSAGE_DETAILS_DART:INTERNAL=[/usr/include][dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart;dart][cfound components: dart ][v(6.6)]
//Details about finding DL
FIND_PACKAGE_MESSAGE_DETAILS_DL:INTERNAL=[true][v()]
//Details about finding JSONCPP
FIND_PACKAGE_MESSAGE_DETAILS_JSONCPP:INTERNAL=[TRUE][v()]
//Details about finding PY_em
FIND_PACKAGE_MESSAGE_DETAILS_PY_em:INTERNAL=[/home/<USER>/anaconda3/lib/python3.9/site-packages/em.py][v()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v0.29.1()]
//Details about finding Protobuf
FIND_PACKAGE_MESSAGE_DETAILS_Protobuf:INTERNAL=[/usr/lib/x86_64-linux-gnu/libprotobuf.so;-lpthread][/usr/include][v3.6.1(3)]
//Details about finding PythonInterp
FIND_PACKAGE_MESSAGE_DETAILS_PythonInterp:INTERNAL=[/home/<USER>/anaconda3/bin/python3][v3.9.12()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding UUID
FIND_PACKAGE_MESSAGE_DETAILS_UUID:INTERNAL=[TRUE][v()]
//Details about finding YAML
FIND_PACKAGE_MESSAGE_DETAILS_YAML:INTERNAL=[TRUE][v()]
//Details about finding ZIP
FIND_PACKAGE_MESSAGE_DETAILS_ZIP:INTERNAL=[TRUE][v()]
//Details about finding assimp
FIND_PACKAGE_MESSAGE_DETAILS_assimp:INTERNAL=[/usr/include][/usr/lib/x86_64-linux-gnu/libassimp.so][v5.0.0()]
//Details about finding ccd
FIND_PACKAGE_MESSAGE_DETAILS_ccd:INTERNAL=[/usr/include][/usr/lib/x86_64-linux-gnu/libccd.so][v2.0(2.0)]
//Details about finding fcl
FIND_PACKAGE_MESSAGE_DETAILS_fcl:INTERNAL=[/usr/include][/usr/lib/x86_64-linux-gnu/libfcl.so][v0.5.0(0.3.2)]
GMOCK_FROM_SOURCE_FOUND:INTERNAL=TRUE
GMOCK_FROM_SOURCE_INCLUDE_DIRS:INTERNAL=/usr/src/googletest/googlemock/include
GMOCK_FROM_SOURCE_LIBRARIES:INTERNAL=gmock
GMOCK_FROM_SOURCE_LIBRARY_DIRS:INTERNAL=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/gmock
GMOCK_FROM_SOURCE_MAIN_LIBRARIES:INTERNAL=gmock_main
GTEST_FROM_SOURCE_FOUND:INTERNAL=TRUE
GTEST_FROM_SOURCE_INCLUDE_DIRS:INTERNAL=/usr/include
GTEST_FROM_SOURCE_LIBRARIES:INTERNAL=gtest
GTEST_FROM_SOURCE_LIBRARY_DIRS:INTERNAL=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/build/gtest
GTEST_FROM_SOURCE_MAIN_LIBRARIES:INTERNAL=gtest_main
JSONCPP_CFLAGS:INTERNAL=-I/usr/include/jsoncpp
JSONCPP_CFLAGS_I:INTERNAL=
JSONCPP_CFLAGS_OTHER:INTERNAL=
JSONCPP_INCLUDEDIR:INTERNAL=/usr/include/jsoncpp
JSONCPP_INCLUDE_DIRS:INTERNAL=/usr/include/jsoncpp
JSONCPP_LDFLAGS:INTERNAL=-ljsoncpp
JSONCPP_LDFLAGS_OTHER:INTERNAL=
JSONCPP_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
JSONCPP_LIBRARIES:INTERNAL=jsoncpp
JSONCPP_LIBRARY_DIRS:INTERNAL=
//ADVANCED property for variable: JSONCPP_LIBRARY_jsoncpp
JSONCPP_LIBRARY_jsoncpp-ADVANCED:INTERNAL=1
JSONCPP_LIBS:INTERNAL=
JSONCPP_LIBS_L:INTERNAL=
JSONCPP_LIBS_OTHER:INTERNAL=
JSONCPP_LIBS_PATHS:INTERNAL=
JSONCPP_MODULE_NAME:INTERNAL=jsoncpp
JSONCPP_PREFIX:INTERNAL=/usr
JSONCPP_STATIC_CFLAGS:INTERNAL=-I/usr/include/jsoncpp
JSONCPP_STATIC_CFLAGS_I:INTERNAL=
JSONCPP_STATIC_CFLAGS_OTHER:INTERNAL=
JSONCPP_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/jsoncpp
JSONCPP_STATIC_LDFLAGS:INTERNAL=-ljsoncpp
JSONCPP_STATIC_LDFLAGS_OTHER:INTERNAL=
JSONCPP_STATIC_LIBDIR:INTERNAL=
JSONCPP_STATIC_LIBRARIES:INTERNAL=jsoncpp
JSONCPP_STATIC_LIBRARY_DIRS:INTERNAL=
JSONCPP_STATIC_LIBS:INTERNAL=
JSONCPP_STATIC_LIBS_L:INTERNAL=
JSONCPP_STATIC_LIBS_OTHER:INTERNAL=
JSONCPP_STATIC_LIBS_PATHS:INTERNAL=
JSONCPP_VERSION:INTERNAL=1.7.4
JSONCPP_jsoncpp_INCLUDEDIR:INTERNAL=
JSONCPP_jsoncpp_LIBDIR:INTERNAL=
JSONCPP_jsoncpp_PREFIX:INTERNAL=
JSONCPP_jsoncpp_VERSION:INTERNAL=
MOSQUITTO_CFLAGS:INTERNAL=
MOSQUITTO_CFLAGS_I:INTERNAL=
MOSQUITTO_CFLAGS_OTHER:INTERNAL=
MOSQUITTO_FOUND:INTERNAL=1
MOSQUITTO_INCLUDEDIR:INTERNAL=/usr/include
MOSQUITTO_INCLUDE_DIRS:INTERNAL=
MOSQUITTO_LDFLAGS:INTERNAL=-lmosquitto
MOSQUITTO_LDFLAGS_OTHER:INTERNAL=
MOSQUITTO_LIBDIR:INTERNAL=/usr/lib
MOSQUITTO_LIBRARIES:INTERNAL=mosquitto
MOSQUITTO_LIBRARY_DIRS:INTERNAL=
MOSQUITTO_LIBS:INTERNAL=
MOSQUITTO_LIBS_L:INTERNAL=
MOSQUITTO_LIBS_OTHER:INTERNAL=
MOSQUITTO_LIBS_PATHS:INTERNAL=
MOSQUITTO_MODULE_NAME:INTERNAL=libmosquitto
MOSQUITTO_PREFIX:INTERNAL=/usr
MOSQUITTO_STATIC_CFLAGS:INTERNAL=
MOSQUITTO_STATIC_CFLAGS_I:INTERNAL=
MOSQUITTO_STATIC_CFLAGS_OTHER:INTERNAL=
MOSQUITTO_STATIC_INCLUDE_DIRS:INTERNAL=
MOSQUITTO_STATIC_LDFLAGS:INTERNAL=-lmosquitto
MOSQUITTO_STATIC_LDFLAGS_OTHER:INTERNAL=
MOSQUITTO_STATIC_LIBDIR:INTERNAL=
MOSQUITTO_STATIC_LIBRARIES:INTERNAL=mosquitto
MOSQUITTO_STATIC_LIBRARY_DIRS:INTERNAL=
MOSQUITTO_STATIC_LIBS:INTERNAL=
MOSQUITTO_STATIC_LIBS_L:INTERNAL=
MOSQUITTO_STATIC_LIBS_OTHER:INTERNAL=
MOSQUITTO_STATIC_LIBS_PATHS:INTERNAL=
MOSQUITTO_VERSION:INTERNAL=1.6.9
MOSQUITTO_libmosquitto_INCLUDEDIR:INTERNAL=
MOSQUITTO_libmosquitto_LIBDIR:INTERNAL=
MOSQUITTO_libmosquitto_PREFIX:INTERNAL=
MOSQUITTO_libmosquitto_VERSION:INTERNAL=
//ADVANCED property for variable: OGRE_CONFIG_INCLUDE_DIR
OGRE_CONFIG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_INCLUDE_DIR
OGRE_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_LIBRARY_DBG
OGRE_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_LIBRARY_FWK
OGRE_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_LIBRARY_REL
OGRE_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_MEDIA_DIR
OGRE_MEDIA_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Overlay_INCLUDE_DIR
OGRE_Overlay_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Overlay_LIBRARY_DBG
OGRE_Overlay_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Overlay_LIBRARY_FWK
OGRE_Overlay_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Overlay_LIBRARY_REL
OGRE_Overlay_LIBRARY_REL-ADVANCED:INTERNAL=1
OGRE_PKGC_CFLAGS:INTERNAL=-pthread;-I/usr/include/OGRE
OGRE_PKGC_CFLAGS_I:INTERNAL=
OGRE_PKGC_CFLAGS_OTHER:INTERNAL=-pthread
OGRE_PKGC_FOUND:INTERNAL=1
OGRE_PKGC_INCLUDEDIR:INTERNAL=/usr/include
OGRE_PKGC_INCLUDE_DIRS:INTERNAL=/usr/include/OGRE
OGRE_PKGC_LDFLAGS:INTERNAL=-lOgreMain;-lpthread
OGRE_PKGC_LDFLAGS_OTHER:INTERNAL=
OGRE_PKGC_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
OGRE_PKGC_LIBRARIES:INTERNAL=OgreMain;pthread
OGRE_PKGC_LIBRARY_DIRS:INTERNAL=
OGRE_PKGC_LIBS:INTERNAL=
OGRE_PKGC_LIBS_L:INTERNAL=
OGRE_PKGC_LIBS_OTHER:INTERNAL=
OGRE_PKGC_LIBS_PATHS:INTERNAL=
OGRE_PKGC_MODULE_NAME:INTERNAL=OGRE
OGRE_PKGC_OGRE_INCLUDEDIR:INTERNAL=
OGRE_PKGC_OGRE_LIBDIR:INTERNAL=
OGRE_PKGC_OGRE_PREFIX:INTERNAL=
OGRE_PKGC_OGRE_VERSION:INTERNAL=
OGRE_PKGC_PREFIX:INTERNAL=/usr
OGRE_PKGC_STATIC_CFLAGS:INTERNAL=-pthread;-I/usr/include/OGRE
OGRE_PKGC_STATIC_CFLAGS_I:INTERNAL=
OGRE_PKGC_STATIC_CFLAGS_OTHER:INTERNAL=-pthread
OGRE_PKGC_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/OGRE
OGRE_PKGC_STATIC_LDFLAGS:INTERNAL=-lOgreMain;-lpthread
OGRE_PKGC_STATIC_LDFLAGS_OTHER:INTERNAL=
OGRE_PKGC_STATIC_LIBDIR:INTERNAL=
OGRE_PKGC_STATIC_LIBRARIES:INTERNAL=OgreMain;pthread
OGRE_PKGC_STATIC_LIBRARY_DIRS:INTERNAL=
OGRE_PKGC_STATIC_LIBS:INTERNAL=
OGRE_PKGC_STATIC_LIBS_L:INTERNAL=
OGRE_PKGC_STATIC_LIBS_OTHER:INTERNAL=
OGRE_PKGC_STATIC_LIBS_PATHS:INTERNAL=
OGRE_PKGC_VERSION:INTERNAL=1.9.0
//ADVANCED property for variable: OGRE_PLUGIN_DIR_DBG
OGRE_PLUGIN_DIR_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_PLUGIN_DIR_REL
OGRE_PLUGIN_DIR_REL-ADVANCED:INTERNAL=1
//x
OGRE_PREFIX_WATCH_INT_CHECK:INTERNAL=/opt/ogre;/opt/OGRE;/usr/lib/ogre;/usr/lib/OGRE;/usr/local/lib/ogre;/usr/local/lib/OGRE;/home/<USER>/ogre;/home/<USER>/OGRE;NOTFOUND;NOTFOUND
//ADVANCED property for variable: OGRE_Paging_INCLUDE_DIR
OGRE_Paging_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Paging_LIBRARY_DBG
OGRE_Paging_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Paging_LIBRARY_FWK
OGRE_Paging_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Paging_LIBRARY_REL
OGRE_Paging_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_BSPSceneManager_INCLUDE_DIR
OGRE_Plugin_BSPSceneManager_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_BSPSceneManager_LIBRARY_DBG
OGRE_Plugin_BSPSceneManager_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_BSPSceneManager_LIBRARY_FWK
OGRE_Plugin_BSPSceneManager_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_BSPSceneManager_LIBRARY_REL
OGRE_Plugin_BSPSceneManager_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_CgProgramManager_INCLUDE_DIR
OGRE_Plugin_CgProgramManager_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_CgProgramManager_LIBRARY_DBG
OGRE_Plugin_CgProgramManager_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_CgProgramManager_LIBRARY_FWK
OGRE_Plugin_CgProgramManager_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_CgProgramManager_LIBRARY_REL
OGRE_Plugin_CgProgramManager_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_OctreeSceneManager_INCLUDE_DIR
OGRE_Plugin_OctreeSceneManager_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_OctreeSceneManager_LIBRARY_DBG
OGRE_Plugin_OctreeSceneManager_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_OctreeSceneManager_LIBRARY_FWK
OGRE_Plugin_OctreeSceneManager_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_OctreeSceneManager_LIBRARY_REL
OGRE_Plugin_OctreeSceneManager_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_OctreeZone_INCLUDE_DIR
OGRE_Plugin_OctreeZone_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_OctreeZone_LIBRARY_DBG
OGRE_Plugin_OctreeZone_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_OctreeZone_LIBRARY_FWK
OGRE_Plugin_OctreeZone_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_OctreeZone_LIBRARY_REL
OGRE_Plugin_OctreeZone_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_PCZSceneManager_INCLUDE_DIR
OGRE_Plugin_PCZSceneManager_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_PCZSceneManager_LIBRARY_DBG
OGRE_Plugin_PCZSceneManager_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_PCZSceneManager_LIBRARY_FWK
OGRE_Plugin_PCZSceneManager_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_PCZSceneManager_LIBRARY_REL
OGRE_Plugin_PCZSceneManager_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_ParticleFX_INCLUDE_DIR
OGRE_Plugin_ParticleFX_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_ParticleFX_LIBRARY_DBG
OGRE_Plugin_ParticleFX_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_ParticleFX_LIBRARY_FWK
OGRE_Plugin_ParticleFX_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Plugin_ParticleFX_LIBRARY_REL
OGRE_Plugin_ParticleFX_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Property_INCLUDE_DIR
OGRE_Property_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Property_LIBRARY_DBG
OGRE_Property_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Property_LIBRARY_REL
OGRE_Property_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RTShaderSystem_INCLUDE_DIR
OGRE_RTShaderSystem_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RTShaderSystem_LIBRARY_DBG
OGRE_RTShaderSystem_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RTShaderSystem_LIBRARY_REL
OGRE_RTShaderSystem_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_Direct3D11_INCLUDE_DIR
OGRE_RenderSystem_Direct3D11_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_Direct3D11_LIBRARY_DBG
OGRE_RenderSystem_Direct3D11_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_Direct3D11_LIBRARY_FWK
OGRE_RenderSystem_Direct3D11_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_Direct3D11_LIBRARY_REL
OGRE_RenderSystem_Direct3D11_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_Direct3D9_INCLUDE_DIR
OGRE_RenderSystem_Direct3D9_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_Direct3D9_LIBRARY_DBG
OGRE_RenderSystem_Direct3D9_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_Direct3D9_LIBRARY_FWK
OGRE_RenderSystem_Direct3D9_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_Direct3D9_LIBRARY_REL
OGRE_RenderSystem_Direct3D9_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GL3Plus_INCLUDE_DIR
OGRE_RenderSystem_GL3Plus_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GL3Plus_LIBRARY_DBG
OGRE_RenderSystem_GL3Plus_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GL3Plus_LIBRARY_FWK
OGRE_RenderSystem_GL3Plus_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GL3Plus_LIBRARY_REL
OGRE_RenderSystem_GL3Plus_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GLES2_INCLUDE_DIR
OGRE_RenderSystem_GLES2_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GLES2_LIBRARY_DBG
OGRE_RenderSystem_GLES2_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GLES2_LIBRARY_FWK
OGRE_RenderSystem_GLES2_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GLES2_LIBRARY_REL
OGRE_RenderSystem_GLES2_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GLES_INCLUDE_DIR
OGRE_RenderSystem_GLES_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GLES_LIBRARY_DBG
OGRE_RenderSystem_GLES_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GLES_LIBRARY_FWK
OGRE_RenderSystem_GLES_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GLES_LIBRARY_REL
OGRE_RenderSystem_GLES_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GL_INCLUDE_DIR
OGRE_RenderSystem_GL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GL_LIBRARY_DBG
OGRE_RenderSystem_GL_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GL_LIBRARY_FWK
OGRE_RenderSystem_GL_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_RenderSystem_GL_LIBRARY_REL
OGRE_RenderSystem_GL_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Terrain_INCLUDE_DIR
OGRE_Terrain_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Terrain_LIBRARY_DBG
OGRE_Terrain_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Terrain_LIBRARY_FWK
OGRE_Terrain_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Terrain_LIBRARY_REL
OGRE_Terrain_LIBRARY_REL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Volume_INCLUDE_DIR
OGRE_Volume_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Volume_LIBRARY_DBG
OGRE_Volume_LIBRARY_DBG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Volume_LIBRARY_FWK
OGRE_Volume_LIBRARY_FWK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OGRE_Volume_LIBRARY_REL
OGRE_Volume_LIBRARY_REL-ADVANCED:INTERNAL=1
PC_ASSIMP_CFLAGS:INTERNAL=-I/usr/../include/include
PC_ASSIMP_CFLAGS_I:INTERNAL=
PC_ASSIMP_CFLAGS_OTHER:INTERNAL=
PC_ASSIMP_FOUND:INTERNAL=1
PC_ASSIMP_INCLUDEDIR:INTERNAL=/usr/../include/include
PC_ASSIMP_INCLUDE_DIRS:INTERNAL=/usr/../include/include
PC_ASSIMP_LDFLAGS:INTERNAL=-lassimp
PC_ASSIMP_LDFLAGS_OTHER:INTERNAL=
PC_ASSIMP_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_ASSIMP_LIBRARIES:INTERNAL=assimp
PC_ASSIMP_LIBRARY_DIRS:INTERNAL=
PC_ASSIMP_LIBS:INTERNAL=
PC_ASSIMP_LIBS_L:INTERNAL=
PC_ASSIMP_LIBS_OTHER:INTERNAL=
PC_ASSIMP_LIBS_PATHS:INTERNAL=
PC_ASSIMP_MODULE_NAME:INTERNAL=assimp
PC_ASSIMP_PREFIX:INTERNAL=/usr
PC_ASSIMP_STATIC_CFLAGS:INTERNAL=-I/usr/../include/include
PC_ASSIMP_STATIC_CFLAGS_I:INTERNAL=
PC_ASSIMP_STATIC_CFLAGS_OTHER:INTERNAL=
PC_ASSIMP_STATIC_INCLUDE_DIRS:INTERNAL=/usr/../include/include
PC_ASSIMP_STATIC_LDFLAGS:INTERNAL=-lassimp;-lstdc++;-lz
PC_ASSIMP_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_ASSIMP_STATIC_LIBDIR:INTERNAL=
PC_ASSIMP_STATIC_LIBRARIES:INTERNAL=assimp;stdc++;z
PC_ASSIMP_STATIC_LIBRARY_DIRS:INTERNAL=
PC_ASSIMP_STATIC_LIBS:INTERNAL=
PC_ASSIMP_STATIC_LIBS_L:INTERNAL=
PC_ASSIMP_STATIC_LIBS_OTHER:INTERNAL=
PC_ASSIMP_STATIC_LIBS_PATHS:INTERNAL=
PC_ASSIMP_VERSION:INTERNAL=5.0.0
PC_ASSIMP_assimp_INCLUDEDIR:INTERNAL=
PC_ASSIMP_assimp_LIBDIR:INTERNAL=
PC_ASSIMP_assimp_PREFIX:INTERNAL=
PC_ASSIMP_assimp_VERSION:INTERNAL=
PC_CCD_CFLAGS:INTERNAL=
PC_CCD_CFLAGS_I:INTERNAL=
PC_CCD_CFLAGS_OTHER:INTERNAL=
PC_CCD_FOUND:INTERNAL=1
PC_CCD_INCLUDEDIR:INTERNAL=/usr/include
PC_CCD_INCLUDE_DIRS:INTERNAL=
PC_CCD_LDFLAGS:INTERNAL=-lccd;-lm
PC_CCD_LDFLAGS_OTHER:INTERNAL=
PC_CCD_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_CCD_LIBRARIES:INTERNAL=ccd;m
PC_CCD_LIBRARY_DIRS:INTERNAL=
PC_CCD_LIBS:INTERNAL=
PC_CCD_LIBS_L:INTERNAL=
PC_CCD_LIBS_OTHER:INTERNAL=
PC_CCD_LIBS_PATHS:INTERNAL=
PC_CCD_MODULE_NAME:INTERNAL=ccd
PC_CCD_PREFIX:INTERNAL=/usr
PC_CCD_STATIC_CFLAGS:INTERNAL=
PC_CCD_STATIC_CFLAGS_I:INTERNAL=
PC_CCD_STATIC_CFLAGS_OTHER:INTERNAL=
PC_CCD_STATIC_INCLUDE_DIRS:INTERNAL=
PC_CCD_STATIC_LDFLAGS:INTERNAL=-lccd;-lm
PC_CCD_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_CCD_STATIC_LIBDIR:INTERNAL=
PC_CCD_STATIC_LIBRARIES:INTERNAL=ccd;m
PC_CCD_STATIC_LIBRARY_DIRS:INTERNAL=
PC_CCD_STATIC_LIBS:INTERNAL=
PC_CCD_STATIC_LIBS_L:INTERNAL=
PC_CCD_STATIC_LIBS_OTHER:INTERNAL=
PC_CCD_STATIC_LIBS_PATHS:INTERNAL=
PC_CCD_VERSION:INTERNAL=2.0
PC_CCD_ccd_INCLUDEDIR:INTERNAL=
PC_CCD_ccd_LIBDIR:INTERNAL=
PC_CCD_ccd_PREFIX:INTERNAL=
PC_CCD_ccd_VERSION:INTERNAL=
PC_CURL_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
PC_CURL_CFLAGS_I:INTERNAL=
PC_CURL_CFLAGS_OTHER:INTERNAL=
PC_CURL_FOUND:INTERNAL=1
PC_CURL_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
PC_CURL_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
PC_CURL_LDFLAGS:INTERNAL=-lcurl
PC_CURL_LDFLAGS_OTHER:INTERNAL=
PC_CURL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_CURL_LIBRARIES:INTERNAL=curl
PC_CURL_LIBRARY_DIRS:INTERNAL=
PC_CURL_LIBS:INTERNAL=
PC_CURL_LIBS_L:INTERNAL=
PC_CURL_LIBS_OTHER:INTERNAL=
PC_CURL_LIBS_PATHS:INTERNAL=
PC_CURL_MODULE_NAME:INTERNAL=libcurl
PC_CURL_PREFIX:INTERNAL=/usr
PC_CURL_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
PC_CURL_STATIC_CFLAGS_I:INTERNAL=
PC_CURL_STATIC_CFLAGS_OTHER:INTERNAL=
PC_CURL_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
PC_CURL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu/mit-krb5;-lcurl;-lnghttp2;-lidn2;-lrtmp;-lssh;-lpsl;-lssl;-lcrypto;-lssl;-lcrypto;-Wl,-Bsymbolic-functions;-Wl,-z,relro;-lgssapi_krb5;-lkrb5;-lk5crypto;-lcom_err;-llber;-lldap;-llber;-lbrotlidec;-lz
PC_CURL_STATIC_LDFLAGS_OTHER:INTERNAL=-Wl,-Bsymbolic-functions;-Wl,-z,relro
PC_CURL_STATIC_LIBDIR:INTERNAL=
PC_CURL_STATIC_LIBRARIES:INTERNAL=curl;nghttp2;idn2;rtmp;ssh;psl;ssl;crypto;ssl;crypto;gssapi_krb5;krb5;k5crypto;com_err;lber;ldap;lber;brotlidec;z
PC_CURL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu/mit-krb5
PC_CURL_STATIC_LIBS:INTERNAL=
PC_CURL_STATIC_LIBS_L:INTERNAL=
PC_CURL_STATIC_LIBS_OTHER:INTERNAL=
PC_CURL_STATIC_LIBS_PATHS:INTERNAL=
PC_CURL_VERSION:INTERNAL=7.68.0
PC_CURL_libcurl_INCLUDEDIR:INTERNAL=
PC_CURL_libcurl_LIBDIR:INTERNAL=
PC_CURL_libcurl_PREFIX:INTERNAL=
PC_CURL_libcurl_VERSION:INTERNAL=
PC_FCL_CFLAGS:INTERNAL=-std=c++11
PC_FCL_CFLAGS_I:INTERNAL=
PC_FCL_CFLAGS_OTHER:INTERNAL=-std=c++11
PC_FCL_FOUND:INTERNAL=1
PC_FCL_INCLUDEDIR:INTERNAL=/usr/include
PC_FCL_INCLUDE_DIRS:INTERNAL=
PC_FCL_LDFLAGS:INTERNAL=-lfcl
PC_FCL_LDFLAGS_OTHER:INTERNAL=
PC_FCL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_FCL_LIBRARIES:INTERNAL=fcl
PC_FCL_LIBRARY_DIRS:INTERNAL=
PC_FCL_LIBS:INTERNAL=
PC_FCL_LIBS_L:INTERNAL=
PC_FCL_LIBS_OTHER:INTERNAL=
PC_FCL_LIBS_PATHS:INTERNAL=
PC_FCL_MODULE_NAME:INTERNAL=fcl
PC_FCL_PREFIX:INTERNAL=/usr
PC_FCL_STATIC_CFLAGS:INTERNAL=-std=c++11
PC_FCL_STATIC_CFLAGS_I:INTERNAL=
PC_FCL_STATIC_CFLAGS_OTHER:INTERNAL=-std=c++11
PC_FCL_STATIC_INCLUDE_DIRS:INTERNAL=
PC_FCL_STATIC_LDFLAGS:INTERNAL=-lfcl
PC_FCL_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_FCL_STATIC_LIBDIR:INTERNAL=
PC_FCL_STATIC_LIBRARIES:INTERNAL=fcl
PC_FCL_STATIC_LIBRARY_DIRS:INTERNAL=
PC_FCL_STATIC_LIBS:INTERNAL=
PC_FCL_STATIC_LIBS_L:INTERNAL=
PC_FCL_STATIC_LIBS_OTHER:INTERNAL=
PC_FCL_STATIC_LIBS_PATHS:INTERNAL=
PC_FCL_VERSION:INTERNAL=0.5.0
PC_FCL_fcl_INCLUDEDIR:INTERNAL=
PC_FCL_fcl_LIBDIR:INTERNAL=
PC_FCL_fcl_PREFIX:INTERNAL=
PC_FCL_fcl_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PYTHON_EXECUTABLE
PYTHON_EXECUTABLE-ADVANCED:INTERNAL=1
//This needs to be in PYTHONPATH when 'setup.py install' is called.
//  And it needs to match.  But setuptools won't tell us where
// it will install things.
PYTHON_INSTALL_DIR:INTERNAL=lib/python3/dist-packages
//ADVANCED property for variable: Protobuf_INCLUDE_DIR
Protobuf_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_LIBRARY_DEBUG
Protobuf_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_LIBRARY_RELEASE
Protobuf_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_LITE_LIBRARY_DEBUG
Protobuf_LITE_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_LITE_LIBRARY_RELEASE
Protobuf_LITE_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_PROTOC_EXECUTABLE
Protobuf_PROTOC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_PROTOC_LIBRARY_DEBUG
Protobuf_PROTOC_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Protobuf_PROTOC_LIBRARY_RELEASE
Protobuf_PROTOC_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
TINYXML2_CFLAGS:INTERNAL=
TINYXML2_CFLAGS_I:INTERNAL=
TINYXML2_CFLAGS_OTHER:INTERNAL=
TINYXML2_INCLUDEDIR:INTERNAL=/usr/include
TINYXML2_LDFLAGS:INTERNAL=-ltinyxml2
TINYXML2_LDFLAGS_OTHER:INTERNAL=
TINYXML2_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
TINYXML2_LIBRARIES:INTERNAL=tinyxml2
//ADVANCED property for variable: TINYXML2_LIBRARY_/usr/lib/x86_64-linux-gnu/libtinyxml2.so
TINYXML2_LIBRARY_/usr/lib/x86_64-linux-gnu/libtinyxml2.so-ADVANCED:INTERNAL=1
TINYXML2_LIBRARY_DIRS:INTERNAL=
//ADVANCED property for variable: TINYXML2_LIBRARY_tinyxml2
TINYXML2_LIBRARY_tinyxml2-ADVANCED:INTERNAL=1
TINYXML2_LIBS:INTERNAL=
TINYXML2_LIBS_L:INTERNAL=
TINYXML2_LIBS_OTHER:INTERNAL=
TINYXML2_LIBS_PATHS:INTERNAL=
TINYXML2_MODULE_NAME:INTERNAL=tinyxml2
TINYXML2_PREFIX:INTERNAL=/usr
TINYXML2_STATIC_CFLAGS:INTERNAL=
TINYXML2_STATIC_CFLAGS_I:INTERNAL=
TINYXML2_STATIC_CFLAGS_OTHER:INTERNAL=
TINYXML2_STATIC_INCLUDE_DIRS:INTERNAL=
TINYXML2_STATIC_LDFLAGS:INTERNAL=-ltinyxml2
TINYXML2_STATIC_LDFLAGS_OTHER:INTERNAL=
TINYXML2_STATIC_LIBDIR:INTERNAL=
TINYXML2_STATIC_LIBRARIES:INTERNAL=tinyxml2
TINYXML2_STATIC_LIBRARY_DIRS:INTERNAL=
TINYXML2_STATIC_LIBS:INTERNAL=
TINYXML2_STATIC_LIBS_L:INTERNAL=
TINYXML2_STATIC_LIBS_OTHER:INTERNAL=
TINYXML2_STATIC_LIBS_PATHS:INTERNAL=
TINYXML2_VERSION:INTERNAL=6.2.0
TINYXML2_tinyxml2_INCLUDEDIR:INTERNAL=
TINYXML2_tinyxml2_LIBDIR:INTERNAL=
TINYXML2_tinyxml2_PREFIX:INTERNAL=
TINYXML2_tinyxml2_VERSION:INTERNAL=
UUID_CFLAGS:INTERNAL=-I/usr/include/uuid
UUID_CFLAGS_I:INTERNAL=
UUID_CFLAGS_OTHER:INTERNAL=
UUID_INCLUDEDIR:INTERNAL=/usr/include
UUID_INCLUDE_DIRS:INTERNAL=/usr/include/uuid
UUID_LDFLAGS:INTERNAL=-luuid
UUID_LDFLAGS_OTHER:INTERNAL=
UUID_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
UUID_LIBRARIES:INTERNAL=uuid
//ADVANCED property for variable: UUID_LIBRARY_/usr/lib/x86_64-linux-gnu/libuuid.so
UUID_LIBRARY_/usr/lib/x86_64-linux-gnu/libuuid.so-ADVANCED:INTERNAL=1
UUID_LIBRARY_DIRS:INTERNAL=
//ADVANCED property for variable: UUID_LIBRARY_uuid
UUID_LIBRARY_uuid-ADVANCED:INTERNAL=1
UUID_LIBS:INTERNAL=
UUID_LIBS_L:INTERNAL=
UUID_LIBS_OTHER:INTERNAL=
UUID_LIBS_PATHS:INTERNAL=
UUID_MODULE_NAME:INTERNAL=uuid
UUID_PREFIX:INTERNAL=/usr
UUID_STATIC_CFLAGS:INTERNAL=-I/usr/include/uuid
UUID_STATIC_CFLAGS_I:INTERNAL=
UUID_STATIC_CFLAGS_OTHER:INTERNAL=
UUID_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/uuid
UUID_STATIC_LDFLAGS:INTERNAL=-luuid
UUID_STATIC_LDFLAGS_OTHER:INTERNAL=
UUID_STATIC_LIBDIR:INTERNAL=
UUID_STATIC_LIBRARIES:INTERNAL=uuid
UUID_STATIC_LIBRARY_DIRS:INTERNAL=
UUID_STATIC_LIBS:INTERNAL=
UUID_STATIC_LIBS_L:INTERNAL=
UUID_STATIC_LIBS_OTHER:INTERNAL=
UUID_STATIC_LIBS_PATHS:INTERNAL=
UUID_VERSION:INTERNAL=2.34.0
UUID_uuid_INCLUDEDIR:INTERNAL=
UUID_uuid_LIBDIR:INTERNAL=
UUID_uuid_PREFIX:INTERNAL=
UUID_uuid_VERSION:INTERNAL=
YAML_CFLAGS:INTERNAL=
YAML_CFLAGS_I:INTERNAL=
YAML_CFLAGS_OTHER:INTERNAL=
YAML_INCLUDEDIR:INTERNAL=/usr/include
YAML_LDFLAGS:INTERNAL=-lyaml
YAML_LDFLAGS_OTHER:INTERNAL=
YAML_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
YAML_LIBRARIES:INTERNAL=yaml
YAML_LIBRARY_DIRS:INTERNAL=
//ADVANCED property for variable: YAML_LIBRARY_yaml
YAML_LIBRARY_yaml-ADVANCED:INTERNAL=1
YAML_LIBS:INTERNAL=
YAML_LIBS_L:INTERNAL=
YAML_LIBS_OTHER:INTERNAL=
YAML_LIBS_PATHS:INTERNAL=
YAML_MODULE_NAME:INTERNAL=yaml-0.1
YAML_PREFIX:INTERNAL=/usr
YAML_STATIC_CFLAGS:INTERNAL=
YAML_STATIC_CFLAGS_I:INTERNAL=
YAML_STATIC_CFLAGS_OTHER:INTERNAL=
YAML_STATIC_INCLUDE_DIRS:INTERNAL=
YAML_STATIC_LDFLAGS:INTERNAL=-lyaml
YAML_STATIC_LDFLAGS_OTHER:INTERNAL=
YAML_STATIC_LIBDIR:INTERNAL=
YAML_STATIC_LIBRARIES:INTERNAL=yaml
YAML_STATIC_LIBRARY_DIRS:INTERNAL=
YAML_STATIC_LIBS:INTERNAL=
YAML_STATIC_LIBS_L:INTERNAL=
YAML_STATIC_LIBS_OTHER:INTERNAL=
YAML_STATIC_LIBS_PATHS:INTERNAL=
YAML_VERSION:INTERNAL=0.2.2
YAML_yaml-0.1_INCLUDEDIR:INTERNAL=
YAML_yaml-0.1_LIBDIR:INTERNAL=
YAML_yaml-0.1_PREFIX:INTERNAL=
YAML_yaml-0.1_VERSION:INTERNAL=
ZIP_CFLAGS:INTERNAL=
ZIP_CFLAGS_I:INTERNAL=
ZIP_CFLAGS_OTHER:INTERNAL=
ZIP_INCLUDEDIR:INTERNAL=/usr/include
ZIP_LDFLAGS:INTERNAL=-L/usr//usr/lib/x86_64-linux-gnu;-lzip
ZIP_LDFLAGS_OTHER:INTERNAL=
ZIP_LIBDIR:INTERNAL=/usr//usr/lib/x86_64-linux-gnu
ZIP_LIBRARIES:INTERNAL=zip
ZIP_LIBRARY_DIRS:INTERNAL=/usr//usr/lib/x86_64-linux-gnu
//ADVANCED property for variable: ZIP_LIBRARY_zip
ZIP_LIBRARY_zip-ADVANCED:INTERNAL=1
ZIP_LIBS:INTERNAL=
ZIP_LIBS_L:INTERNAL=
ZIP_LIBS_OTHER:INTERNAL=
ZIP_LIBS_PATHS:INTERNAL=
ZIP_MODULE_NAME:INTERNAL=libzip
ZIP_PREFIX:INTERNAL=/usr
ZIP_STATIC_CFLAGS:INTERNAL=
ZIP_STATIC_CFLAGS_I:INTERNAL=
ZIP_STATIC_CFLAGS_OTHER:INTERNAL=
ZIP_STATIC_INCLUDE_DIRS:INTERNAL=
ZIP_STATIC_LDFLAGS:INTERNAL=-L/usr//usr/lib/x86_64-linux-gnu;-lzip;-lbz2;-lz
ZIP_STATIC_LDFLAGS_OTHER:INTERNAL=
ZIP_STATIC_LIBDIR:INTERNAL=
ZIP_STATIC_LIBRARIES:INTERNAL=zip;bz2;z
ZIP_STATIC_LIBRARY_DIRS:INTERNAL=/usr//usr/lib/x86_64-linux-gnu
ZIP_STATIC_LIBS:INTERNAL=
ZIP_STATIC_LIBS_L:INTERNAL=
ZIP_STATIC_LIBS_OTHER:INTERNAL=
ZIP_STATIC_LIBS_PATHS:INTERNAL=
ZIP_VERSION:INTERNAL=1.5.1
ZIP_libzip_INCLUDEDIR:INTERNAL=
ZIP_libzip_LIBDIR:INTERNAL=
ZIP_libzip_PREFIX:INTERNAL=
ZIP_libzip_VERSION:INTERNAL=
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/install
__pkg_config_arguments_BULLET:INTERNAL=bullet>=2.82
__pkg_config_arguments_JSONCPP:INTERNAL=jsoncpp
__pkg_config_arguments_MOSQUITTO:INTERNAL=REQUIRED;libmosquitto
__pkg_config_arguments_OGRE_PKGC:INTERNAL=OGRE
__pkg_config_arguments_PC_ASSIMP:INTERNAL=assimp;QUIET
__pkg_config_arguments_PC_CCD:INTERNAL=ccd;QUIET
__pkg_config_arguments_PC_CURL:INTERNAL=QUIET;libcurl
__pkg_config_arguments_PC_FCL:INTERNAL=fcl;QUIET
__pkg_config_arguments_TINYXML2:INTERNAL=tinyxml2
__pkg_config_arguments_UUID:INTERNAL=uuid
__pkg_config_arguments_YAML:INTERNAL=yaml-0.1
__pkg_config_arguments_ZIP:INTERNAL=libzip
__pkg_config_checked_BULLET:INTERNAL=1
__pkg_config_checked_JSONCPP:INTERNAL=1
__pkg_config_checked_MOSQUITTO:INTERNAL=1
__pkg_config_checked_OGRE_PKGC:INTERNAL=1
__pkg_config_checked_PC_ASSIMP:INTERNAL=1
__pkg_config_checked_PC_CCD:INTERNAL=1
__pkg_config_checked_PC_CURL:INTERNAL=1
__pkg_config_checked_PC_FCL:INTERNAL=1
__pkg_config_checked_TINYXML2:INTERNAL=1
__pkg_config_checked_UUID:INTERNAL=1
__pkg_config_checked_YAML:INTERNAL=1
__pkg_config_checked_ZIP:INTERNAL=1
//ADVANCED property for variable: boost_atomic_DIR
boost_atomic_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_date_time_DIR
boost_date_time_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_filesystem_DIR
boost_filesystem_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_headers_DIR
boost_headers_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_iostreams_DIR
boost_iostreams_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_program_options_DIR
boost_program_options_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_regex_DIR
boost_regex_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_system_DIR
boost_system_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_thread_DIR
boost_thread_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gmock_build_tests
gmock_build_tests-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_build_samples
gtest_build_samples-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_build_tests
gtest_build_tests-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_disable_pthreads
gtest_disable_pthreads-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_force_shared_crt
gtest_force_shared_crt-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gtest_hide_internal_symbols
gtest_hide_internal_symbols-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_BULLET_BulletCollision
pkgcfg_lib_BULLET_BulletCollision-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_BULLET_BulletDynamics
pkgcfg_lib_BULLET_BulletDynamics-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_BULLET_BulletSoftBody
pkgcfg_lib_BULLET_BulletSoftBody-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_BULLET_LinearMath
pkgcfg_lib_BULLET_LinearMath-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_JSONCPP_jsoncpp
pkgcfg_lib_JSONCPP_jsoncpp-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_MOSQUITTO_mosquitto
pkgcfg_lib_MOSQUITTO_mosquitto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_OGRE_PKGC_OgreMain
pkgcfg_lib_OGRE_PKGC_OgreMain-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_OGRE_PKGC_pthread
pkgcfg_lib_OGRE_PKGC_pthread-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_ASSIMP_assimp
pkgcfg_lib_PC_ASSIMP_assimp-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_CCD_ccd
pkgcfg_lib_PC_CCD_ccd-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_CCD_m
pkgcfg_lib_PC_CCD_m-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_CURL_curl
pkgcfg_lib_PC_CURL_curl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_FCL_fcl
pkgcfg_lib_PC_FCL_fcl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_TINYXML2_tinyxml2
pkgcfg_lib_TINYXML2_tinyxml2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_UUID_uuid
pkgcfg_lib_UUID_uuid-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_YAML_yaml
pkgcfg_lib_YAML_yaml-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_ZIP_zip
pkgcfg_lib_ZIP_zip-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr//usr/lib/x86_64-linux-gnu

