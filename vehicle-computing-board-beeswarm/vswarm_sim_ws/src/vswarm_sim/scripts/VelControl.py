#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vehicle系统速度控制器 - 兼容BeeSwarm vehicle-computing-board-beeswarm
功能:
1. 监听/{hostname}/vel_cmd话题的速度命令
2. 通过Gazebo的set_model_state服务控制车辆运动
3. 适配vehicle系统的静态车辆模型
4. 提供与无人机系统VelControl.py相同的接口
"""

import rospy
from geometry_msgs.msg import Twist
from gazebo_msgs.srv import SetModelState, GetModelState
from gazebo_msgs.msg import ModelState
import socket
import tf.transformations as tf_trans
import math

class VelocityControllerNode:
    """Vehicle系统速度控制器类"""

    def __init__(self):
        """初始化速度控制器"""
        # 初始化ROS节点
        rospy.init_node('vel_controller_node')
        
        # 获取模型名称（使用主机名）
        self.model_name = socket.gethostname()
        
        # 订阅速度指令话题（兼容BeeSwarm格式）
        vel_cmd_topic = f'/{self.model_name}/vel_cmd'
        self.vel_subscriber = rospy.Subscriber(vel_cmd_topic, Twist, self.velocity_callback)
        
        # 创建Gazebo服务客户端
        rospy.wait_for_service('/gazebo/set_model_state')
        self.set_model_state = rospy.ServiceProxy('/gazebo/set_model_state', SetModelState)
        
        rospy.wait_for_service('/gazebo/get_model_state')
        self.get_model_state = rospy.ServiceProxy('/gazebo/get_model_state', GetModelState)
        
        # 初始化模型状态
        self.model_state = ModelState()
        self.model_state.model_name = self.model_name
        
        # 存储当前速度
        self.current_linear_velocity = [0.0, 0.0, 0.0]
        self.current_angular_velocity = [0.0, 0.0, 0.0]
        
        # 时间管理
        self.last_update_time = rospy.Time.now()
        self.velocity_timeout = rospy.Duration(1.0)  # 速度命令超时时间
        self.last_velocity_time = rospy.Time.now()
        
        # 车辆参数
        self.ground_height = 0.1  # 车辆地面高度
        self.initial_yaw = None  # 记录初始角度用于调试

        # 🛡️ 基于lab_106.world的安全边界配置
        self.safety_bounds = {
            'x_min': -3.0, 'x_max': 3.0,    # 避开左墙(-4.0)和书架(-3.67)
            'y_min': -4.0, 'y_max': 4.0,    # 避开南墙(-5.075)和北墙(+5.0)
            'z_min': 0.05, 'z_max': 1.0
        }
        self.boundary_violations = 0

        rospy.loginfo(f"Vehicle速度控制器初始化完成")
        rospy.loginfo(f"🛡️ Lab_106安全边界: X[{self.safety_bounds['x_min']}, {self.safety_bounds['x_max']}], Y[{self.safety_bounds['y_min']}, {self.safety_bounds['y_max']}]")

    def _is_position_safe(self, x, y):
        """检查位置是否在lab_106安全边界内"""
        return (self.safety_bounds['x_min'] <= x <= self.safety_bounds['x_max'] and
                self.safety_bounds['y_min'] <= y <= self.safety_bounds['y_max'])
        rospy.loginfo(f"模型名称: {self.model_name}")
        rospy.loginfo(f"监听话题: {vel_cmd_topic}")
        rospy.loginfo(f"适配vehicle系统静态模型运动控制")

    def velocity_callback(self, msg):
        """速度命令回调函数"""
        # 存储接收到的速度命令
        self.current_linear_velocity = [msg.linear.x, msg.linear.y, msg.linear.z]
        self.current_angular_velocity = [msg.angular.x, msg.angular.y, msg.angular.z]
        
        # 更新最后接收时间
        self.last_velocity_time = rospy.Time.now()
        
        # 打印接收到的速度命令（调试用）
        if abs(msg.linear.x) > 0.01 or abs(msg.angular.z) > 0.01:
            rospy.loginfo(f"接收速度命令: linear_x={msg.linear.x:.3f}, angular_z={msg.angular.z:.3f}")

    def get_current_model_state(self):
        """获取当前模型状态"""
        try:
            model_state = self.get_model_state(self.model_name, '')
            return model_state
        except rospy.ServiceException as e:
            rospy.logerr(f"获取模型状态失败: {e}")
            return None

    def update_model_state(self):
        """更新模型状态"""
        # 计算时间差
        current_time = rospy.Time.now()
        dt = (current_time - self.last_update_time).to_sec()
        self.last_update_time = current_time
        
        # 获取当前模型状态
        current_state = self.get_current_model_state()
        if current_state is None:
            return
        
        # 更新模型当前位置和姿态
        self.model_state.pose.position.x = current_state.pose.position.x
        self.model_state.pose.position.y = current_state.pose.position.y
        self.model_state.pose.position.z = current_state.pose.position.z
        self.model_state.pose.orientation = current_state.pose.orientation
        
        # 检查速度命令是否超时
        if rospy.Time.now() - self.last_velocity_time > self.velocity_timeout:
            # 超时则停止
            self.current_linear_velocity = [0.0, 0.0, 0.0]
            self.current_angular_velocity = [0.0, 0.0, 0.0]
        
        # 计算新的位置
        # 🚗 全向小车支持：linear.x（前进/后退）、linear.y（左移/右移）、angular.z（转向）
        linear_x = self.current_linear_velocity[0]  # 前进/后退
        linear_y = self.current_linear_velocity[1]  # 左移/右移（全向运动）
        angular_z = self.current_angular_velocity[2]  # 转向

        if abs(linear_x) > 0.001 or abs(linear_y) > 0.001 or abs(angular_z) > 0.001:
            # 获取当前偏航角
            current_orientation = current_state.pose.orientation
            current_yaw = self.quaternion_to_yaw(current_orientation)

            # 记录初始角度（仅第一次）
            if self.initial_yaw is None:
                self.initial_yaw = current_yaw
                rospy.loginfo(f"记录初始角度: {self.initial_yaw:.3f}rad ({self.initial_yaw*180/3.14159:.1f}°)")

            # 🚗 全向小车位置计算（支持linear.x和linear.y）
            # linear.x: 沿车辆前方向移动（前进/后退）
            # linear.y: 沿车辆左方向移动（左移/右移）
            dx_forward = linear_x * math.cos(current_yaw) * dt  # 前进方向的X分量
            dy_forward = linear_x * math.sin(current_yaw) * dt  # 前进方向的Y分量

            dx_left = -linear_y * math.sin(current_yaw) * dt    # 左移方向的X分量
            dy_left = linear_y * math.cos(current_yaw) * dt     # 左移方向的Y分量

            new_x = self.model_state.pose.position.x + dx_forward + dx_left
            new_y = self.model_state.pose.position.y + dy_forward + dy_left
            new_z = self.ground_height  # 保持地面高度

            # 🛡️ 边界安全检查
            if not self._is_position_safe(new_x, new_y):
                self.boundary_violations += 1
                rospy.logwarn(f"🚫 边界违反 #{self.boundary_violations}: 尝试移动到({new_x:.2f}, {new_y:.2f})，超出安全边界")
                rospy.logwarn(f"🛡️ 安全边界: X[{self.safety_bounds['x_min']}, {self.safety_bounds['x_max']}], Y[{self.safety_bounds['y_min']}, {self.safety_bounds['y_max']}]")

                # 限制在边界内
                new_x = max(self.safety_bounds['x_min'], min(self.safety_bounds['x_max'], new_x))
                new_y = max(self.safety_bounds['y_min'], min(self.safety_bounds['y_max'], new_y))
                rospy.loginfo(f"🔧 位置修正到: ({new_x:.2f}, {new_y:.2f})")

            # 调试全向运动
            if abs(linear_y) > 0.001:
                rospy.loginfo(f"🚗 全向运动: linear_x={linear_x:.3f}, linear_y={linear_y:.3f}, yaw={current_yaw:.3f}rad")

            # 计算新的偏航角
            new_yaw = current_yaw + angular_z * dt

            # 🔧 关键修复：不进行角度归一化，允许角度累积
            # 注释掉原来可能的归一化逻辑，让角度自由累积

            # 增强调试信息
            if abs(angular_z) > 0.001:
                yaw_change = angular_z * dt
                total_rotation = new_yaw - self.initial_yaw if hasattr(self, 'initial_yaw') else new_yaw
                rospy.loginfo(f"转向: angular_z={angular_z:.3f}, dt={dt:.3f}, yaw_change={yaw_change:.3f}, current_yaw={current_yaw:.3f}, new_yaw={new_yaw:.3f}, 累积转角={total_rotation:.3f}rad ({total_rotation*180/3.14159:.1f}°)")

            # 更新位置
            self.model_state.pose.position.x = new_x
            self.model_state.pose.position.y = new_y
            self.model_state.pose.position.z = new_z

            # 更新姿态（只改变偏航角）
            try:
                # 🔧 备选方案：如果tf_trans有问题，直接构造四元数
                # 对于纯偏航角旋转：q = [0, 0, sin(yaw/2), cos(yaw/2)]
                # 但先尝试原方法，如果有问题再切换
                new_quaternion = tf_trans.quaternion_from_euler(0, 0, new_yaw)
                self.model_state.pose.orientation.x = new_quaternion[0]
                self.model_state.pose.orientation.y = new_quaternion[1]
                self.model_state.pose.orientation.z = new_quaternion[2]
                self.model_state.pose.orientation.w = new_quaternion[3]

                # 调试：验证四元数转换是否正确
                if abs(angular_z) > 0.001:
                    verify_yaw = self.quaternion_to_yaw(self.model_state.pose.orientation)
                    rospy.loginfo(f"四元数验证: 设置角度={new_yaw:.3f}, 验证角度={verify_yaw:.3f}, 差异={abs(new_yaw-verify_yaw):.3f}")

            except Exception as e:
                rospy.logwarn(f"四元数生成失败: {e}")
                return

            # 应用到Gazebo
            try:
                self.set_model_state(self.model_state)
            except rospy.ServiceException as e:
                rospy.logerr(f"设置模型状态失败: {e}")

    def quaternion_to_yaw(self, quaternion):
        """将四元数转换为偏航角"""
        try:
            euler = tf_trans.euler_from_quaternion([
                quaternion.x, quaternion.y, quaternion.z, quaternion.w
            ])
            return euler[2]  # 返回偏航角
        except Exception as e:
            rospy.logwarn(f"四元数转换失败: {e}")
            return 0.0

    def run(self):
        """运行控制器"""
        rospy.loginfo(f"Vehicle速度控制器开始运行...")
        
        rate = rospy.Rate(20)  # 20Hz更新频率
        
        while not rospy.is_shutdown():
            try:
                self.update_model_state()
                rate.sleep()
            except rospy.ROSInterruptException:
                rospy.loginfo("接收到ROS中断信号")
                break
            except Exception as e:
                rospy.logerr(f"控制器运行错误: {e}")

if __name__ == '__main__':
    try:
        vel_controller = VelocityControllerNode()
        vel_controller.run()
    except rospy.ROSInterruptException:
        rospy.loginfo("Vehicle速度控制器退出")
    except Exception as e:
        rospy.logerr(f"Vehicle速度控制器错误: {e}")
