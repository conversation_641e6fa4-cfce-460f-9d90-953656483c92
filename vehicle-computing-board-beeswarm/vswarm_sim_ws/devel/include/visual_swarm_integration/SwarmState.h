// Generated by gencpp from file visual_swarm_integration/SwarmState.msg
// DO NOT EDIT!


#ifndef VISUAL_SWARM_INTEGRATION_MESSAGE_SWARMSTATE_H
#define VISUAL_SWARM_INTEGRATION_MESSAGE_SWARMSTATE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>

namespace visual_swarm_integration
{
template <class ContainerAllocator>
struct SwarmState_
{
  typedef SwarmState_<ContainerAllocator> Type;

  SwarmState_()
    : header()
    , current_velocity(0.0)
    , current_heading(0.0)
    , velocity_change(0.0)
    , heading_change(0.0)
    , confidence(0.0)
    , behavior_mode()  {
    }
  SwarmState_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , current_velocity(0.0)
    , current_heading(0.0)
    , velocity_change(0.0)
    , heading_change(0.0)
    , confidence(0.0)
    , behavior_mode(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef double _current_velocity_type;
  _current_velocity_type current_velocity;

   typedef double _current_heading_type;
  _current_heading_type current_heading;

   typedef double _velocity_change_type;
  _velocity_change_type velocity_change;

   typedef double _heading_change_type;
  _heading_change_type heading_change;

   typedef double _confidence_type;
  _confidence_type confidence;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _behavior_mode_type;
  _behavior_mode_type behavior_mode;





  typedef boost::shared_ptr< ::visual_swarm_integration::SwarmState_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::visual_swarm_integration::SwarmState_<ContainerAllocator> const> ConstPtr;

}; // struct SwarmState_

typedef ::visual_swarm_integration::SwarmState_<std::allocator<void> > SwarmState;

typedef boost::shared_ptr< ::visual_swarm_integration::SwarmState > SwarmStatePtr;
typedef boost::shared_ptr< ::visual_swarm_integration::SwarmState const> SwarmStateConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::visual_swarm_integration::SwarmState_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::visual_swarm_integration::SwarmState_<ContainerAllocator1> & lhs, const ::visual_swarm_integration::SwarmState_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.current_velocity == rhs.current_velocity &&
    lhs.current_heading == rhs.current_heading &&
    lhs.velocity_change == rhs.velocity_change &&
    lhs.heading_change == rhs.heading_change &&
    lhs.confidence == rhs.confidence &&
    lhs.behavior_mode == rhs.behavior_mode;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::visual_swarm_integration::SwarmState_<ContainerAllocator1> & lhs, const ::visual_swarm_integration::SwarmState_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace visual_swarm_integration

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::visual_swarm_integration::SwarmState_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::visual_swarm_integration::SwarmState_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::visual_swarm_integration::SwarmState_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >
{
  static const char* value()
  {
    return "aeaf139876ee71d5bb7d88ebbbc1fc4e";
  }

  static const char* value(const ::visual_swarm_integration::SwarmState_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xaeaf139876ee71d5ULL;
  static const uint64_t static_value2 = 0xbb7d88ebbbc1fc4eULL;
};

template<class ContainerAllocator>
struct DataType< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >
{
  static const char* value()
  {
    return "visual_swarm_integration/SwarmState";
  }

  static const char* value(const ::visual_swarm_integration::SwarmState_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 集群状态消息\n"
"Header header\n"
"float64 current_velocity      # 当前速度\n"
"float64 current_heading       # 当前航向(弧度)\n"
"float64 velocity_change       # 速度变化\n"
"float64 heading_change        # 航向变化\n"
"float64 confidence           # 行为置信度\n"
"string behavior_mode         # 行为模式 (\"swarm\", \"explore\", \"stop\")\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
;
  }

  static const char* value(const ::visual_swarm_integration::SwarmState_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.current_velocity);
      stream.next(m.current_heading);
      stream.next(m.velocity_change);
      stream.next(m.heading_change);
      stream.next(m.confidence);
      stream.next(m.behavior_mode);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct SwarmState_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::visual_swarm_integration::SwarmState_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::visual_swarm_integration::SwarmState_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "current_velocity: ";
    Printer<double>::stream(s, indent + "  ", v.current_velocity);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "current_heading: ";
    Printer<double>::stream(s, indent + "  ", v.current_heading);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "velocity_change: ";
    Printer<double>::stream(s, indent + "  ", v.velocity_change);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "heading_change: ";
    Printer<double>::stream(s, indent + "  ", v.heading_change);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<double>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "behavior_mode: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.behavior_mode);
  }
};

} // namespace message_operations
} // namespace ros

#endif // VISUAL_SWARM_INTEGRATION_MESSAGE_SWARMSTATE_H
