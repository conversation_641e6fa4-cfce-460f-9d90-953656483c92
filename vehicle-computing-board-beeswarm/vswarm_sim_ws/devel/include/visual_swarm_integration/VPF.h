// Generated by gencpp from file visual_swarm_integration/VPF.msg
// DO NOT EDIT!


#ifndef VISUAL_SWARM_INTEGRATION_MESSAGE_VPF_H
#define VISUAL_SWARM_INTEGRATION_MESSAGE_VPF_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>

namespace visual_swarm_integration
{
template <class ContainerAllocator>
struct VPF_
{
  typedef VPF_<ContainerAllocator> Type;

  VPF_()
    : header()
    , fov(0.0)
    , resolution(0)
    , projection_field()
    , confidence(0.0)
    , detection_count(0)  {
    }
  VPF_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , fov(0.0)
    , resolution(0)
    , projection_field(_alloc)
    , confidence(0.0)
    , detection_count(0)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef double _fov_type;
  _fov_type fov;

   typedef int32_t _resolution_type;
  _resolution_type resolution;

   typedef std::vector<double, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<double>> _projection_field_type;
  _projection_field_type projection_field;

   typedef double _confidence_type;
  _confidence_type confidence;

   typedef int32_t _detection_count_type;
  _detection_count_type detection_count;





  typedef boost::shared_ptr< ::visual_swarm_integration::VPF_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::visual_swarm_integration::VPF_<ContainerAllocator> const> ConstPtr;

}; // struct VPF_

typedef ::visual_swarm_integration::VPF_<std::allocator<void> > VPF;

typedef boost::shared_ptr< ::visual_swarm_integration::VPF > VPFPtr;
typedef boost::shared_ptr< ::visual_swarm_integration::VPF const> VPFConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::visual_swarm_integration::VPF_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::visual_swarm_integration::VPF_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::visual_swarm_integration::VPF_<ContainerAllocator1> & lhs, const ::visual_swarm_integration::VPF_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.fov == rhs.fov &&
    lhs.resolution == rhs.resolution &&
    lhs.projection_field == rhs.projection_field &&
    lhs.confidence == rhs.confidence &&
    lhs.detection_count == rhs.detection_count;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::visual_swarm_integration::VPF_<ContainerAllocator1> & lhs, const ::visual_swarm_integration::VPF_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace visual_swarm_integration

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::visual_swarm_integration::VPF_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::visual_swarm_integration::VPF_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::visual_swarm_integration::VPF_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::visual_swarm_integration::VPF_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::visual_swarm_integration::VPF_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::visual_swarm_integration::VPF_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::visual_swarm_integration::VPF_<ContainerAllocator> >
{
  static const char* value()
  {
    return "26061535f758ed78a5a8d0cb8c294351";
  }

  static const char* value(const ::visual_swarm_integration::VPF_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x26061535f758ed78ULL;
  static const uint64_t static_value2 = 0xa5a8d0cb8c294351ULL;
};

template<class ContainerAllocator>
struct DataType< ::visual_swarm_integration::VPF_<ContainerAllocator> >
{
  static const char* value()
  {
    return "visual_swarm_integration/VPF";
  }

  static const char* value(const ::visual_swarm_integration::VPF_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::visual_swarm_integration::VPF_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 视觉投影场消息\n"
"Header header\n"
"float64 fov                    # 视野角度(弧度)\n"
"int32 resolution              # VPF分辨率\n"
"float64[] projection_field    # 投影场数据 [0,1]\n"
"float64 confidence           # 整体置信度\n"
"int32 detection_count        # 检测目标数量\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
;
  }

  static const char* value(const ::visual_swarm_integration::VPF_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::visual_swarm_integration::VPF_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.fov);
      stream.next(m.resolution);
      stream.next(m.projection_field);
      stream.next(m.confidence);
      stream.next(m.detection_count);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct VPF_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::visual_swarm_integration::VPF_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::visual_swarm_integration::VPF_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "fov: ";
    Printer<double>::stream(s, indent + "  ", v.fov);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "resolution: ";
    Printer<int32_t>::stream(s, indent + "  ", v.resolution);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "projection_field: ";
    if (v.projection_field.empty() || true)
      s << "[";
    for (size_t i = 0; i < v.projection_field.size(); ++i)
    {
      if (true && i > 0)
        s << ", ";
      else if (!true)
        s << std::endl << indent << "  -";
      Printer<double>::stream(s, true ? std::string() : indent + "    ", v.projection_field[i]);
    }
    if (v.projection_field.empty() || true)
      s << "]";
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<double>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "detection_count: ";
    Printer<int32_t>::stream(s, indent + "  ", v.detection_count);
  }
};

} // namespace message_operations
} // namespace ros

#endif // VISUAL_SWARM_INTEGRATION_MESSAGE_VPF_H
