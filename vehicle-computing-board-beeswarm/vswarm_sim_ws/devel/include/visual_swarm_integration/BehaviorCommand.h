// Generated by gencpp from file visual_swarm_integration/BehaviorCommand.msg
// DO NOT EDIT!


#ifndef VISUAL_SWARM_INTEGRATION_MESSAGE_BEHAVIORCOMMAND_H
#define VISUAL_SWARM_INTEGRATION_MESSAGE_BEHAVIORCOMMAND_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <std_msgs/Header.h>

namespace visual_swarm_integration
{
template <class ContainerAllocator>
struct BehaviorCommand_
{
  typedef BehaviorCommand_<ContainerAllocator> Type;

  BehaviorCommand_()
    : header()
    , velocity_change(0.0)
    , heading_change(0.0)
    , confidence(0.0)
    , command_source()  {
    }
  BehaviorCommand_(const ContainerAllocator& _alloc)
    : header(_alloc)
    , velocity_change(0.0)
    , heading_change(0.0)
    , confidence(0.0)
    , command_source(_alloc)  {
  (void)_alloc;
    }



   typedef  ::std_msgs::Header_<ContainerAllocator>  _header_type;
  _header_type header;

   typedef double _velocity_change_type;
  _velocity_change_type velocity_change;

   typedef double _heading_change_type;
  _heading_change_type heading_change;

   typedef double _confidence_type;
  _confidence_type confidence;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _command_source_type;
  _command_source_type command_source;





  typedef boost::shared_ptr< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> const> ConstPtr;

}; // struct BehaviorCommand_

typedef ::visual_swarm_integration::BehaviorCommand_<std::allocator<void> > BehaviorCommand;

typedef boost::shared_ptr< ::visual_swarm_integration::BehaviorCommand > BehaviorCommandPtr;
typedef boost::shared_ptr< ::visual_swarm_integration::BehaviorCommand const> BehaviorCommandConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator1> & lhs, const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator2> & rhs)
{
  return lhs.header == rhs.header &&
    lhs.velocity_change == rhs.velocity_change &&
    lhs.heading_change == rhs.heading_change &&
    lhs.confidence == rhs.confidence &&
    lhs.command_source == rhs.command_source;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator1> & lhs, const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace visual_swarm_integration

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> const>
  : TrueType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >
{
  static const char* value()
  {
    return "c4e554b88fdb2d0953ed529226067b55";
  }

  static const char* value(const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xc4e554b88fdb2d09ULL;
  static const uint64_t static_value2 = 0x53ed529226067b55ULL;
};

template<class ContainerAllocator>
struct DataType< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >
{
  static const char* value()
  {
    return "visual_swarm_integration/BehaviorCommand";
  }

  static const char* value(const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# 行为控制命令消息\n"
"Header header\n"
"float64 velocity_change       # 速度变化量\n"
"float64 heading_change        # 航向变化量\n"
"float64 confidence           # 命令置信度\n"
"string command_source        # 命令来源 (\"visual_swarm\", \"manual\", \"emergency\")\n"
"\n"
"================================================================================\n"
"MSG: std_msgs/Header\n"
"# Standard metadata for higher-level stamped data types.\n"
"# This is generally used to communicate timestamped data \n"
"# in a particular coordinate frame.\n"
"# \n"
"# sequence ID: consecutively increasing ID \n"
"uint32 seq\n"
"#Two-integer timestamp that is expressed as:\n"
"# * stamp.sec: seconds (stamp_secs) since epoch (in Python the variable is called 'secs')\n"
"# * stamp.nsec: nanoseconds since stamp_secs (in Python the variable is called 'nsecs')\n"
"# time-handling sugar is provided by the client library\n"
"time stamp\n"
"#Frame this data is associated with\n"
"string frame_id\n"
;
  }

  static const char* value(const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.header);
      stream.next(m.velocity_change);
      stream.next(m.heading_change);
      stream.next(m.confidence);
      stream.next(m.command_source);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct BehaviorCommand_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::visual_swarm_integration::BehaviorCommand_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "header: ";
    Printer< ::std_msgs::Header_<ContainerAllocator> >::stream(s, indent + "  ", v.header);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "velocity_change: ";
    Printer<double>::stream(s, indent + "  ", v.velocity_change);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "heading_change: ";
    Printer<double>::stream(s, indent + "  ", v.heading_change);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "confidence: ";
    Printer<double>::stream(s, indent + "  ", v.confidence);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "command_source: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.command_source);
  }
};

} // namespace message_operations
} // namespace ros

#endif // VISUAL_SWARM_INTEGRATION_MESSAGE_BEHAVIORCOMMAND_H
