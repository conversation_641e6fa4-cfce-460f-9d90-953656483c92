<?xml version="1.0"?>
<launch>
  <!-- VisualSwarm + BeeSwarm 简化集成启动文件 -->
  <!-- 复用BeeSwarm现有基础设施，无需额外MQTT配置 -->
  
  <!-- 参数配置 -->
  <arg name="vehicle_id" default="vehicle_1" doc="车辆ID"/>
  <arg name="use_visualswarm_cnn" default="false" doc="是否使用VisualSwarm CNN检测"/>
  
  <!-- VisualSwarm Parameters -->
  <rosparam>
    visual_swarm:
      GAM: 0.1
      V0: 125
      ALP0: 125
      ALP1: 0.00075
      BET0: 10
      BET1: 0.001

      vpf_fov: 6.28
      vpf_resolution: 320
      vpf_image_height: 200

      control_frequency: 10.0
      max_linear_velocity: 2.0
      max_angular_velocity: 1.0
      integration_dt: 0.1
      velocity_decay: 0.95
  </rosparam>
  
  <!-- 1. 启动BeeSwarm基础系统 -->
  <!-- 注意：需要先启动sim_server.py和Gazebo环境 -->
  <include file="$(find swarm_experiment)/launch/test_decoupled_system.launch">
    <arg name="vehicle_id" value="$(arg vehicle_id)"/>
  </include>
  
  <!-- 2. 检测系统选择 -->
  <group if="$(arg use_visualswarm_cnn)">
    <!-- 使用VisualSwarm CNN检测 -->
    <node name="visual_swarm_cnn_detector" 
          pkg="visual_swarm_integration" 
          type="cnn_detection_node.py" 
          output="screen">
      <param name="vehicle_id" value="$(arg vehicle_id)"/>
      <param name="model_path" value="$(find visual_swarm_integration)/models/visualswarm_cnn.tflite"/>
      <param name="labelmap_path" value="$(find visual_swarm_integration)/models/labelmap.txt"/>
    </node>
  </group>
  
  <!-- 3. VisualSwarm主节点 -->
  <node name="visual_swarm_main" 
        pkg="visual_swarm_integration" 
        type="visual_swarm_main_node.py" 
        output="screen">
    
    <!-- 基本配置 -->
    <param name="vehicle_id" value="$(arg vehicle_id)"/>
    
    <!-- VisualSwarm参数 -->
    <param name="GAM" value="0.1"/>
    <param name="V0" value="125"/>
    <param name="ALP0" value="125"/>
    <param name="ALP1" value="0.00075"/>
    <param name="BET0" value="10"/>
    <param name="BET1" value="0.001"/>
    
    <!-- 控制参数 -->
    <param name="control_frequency" value="10.0"/>
    <param name="max_linear_velocity" value="2.0"/>
    <param name="max_angular_velocity" value="1.0"/>
    <param name="integration_dt" value="0.1"/>
    <param name="velocity_decay" value="0.95"/>
    
    <!-- VPF参数 -->
    <param name="vpf_fov" value="6.28"/>
    <param name="vpf_resolution" value="320"/>
    <param name="vpf_image_height" value="200"/>
    
    <!-- CNN配置 -->
    <param name="cnn_enabled" value="$(arg use_visualswarm_cnn)"/>
    <param name="cnn_model_path" value="$(find visual_swarm_integration)/models/visualswarm_cnn.tflite"/>
    <param name="cnn_labelmap_path" value="$(find visual_swarm_integration)/models/labelmap.txt"/>
  </node>
  
  <!-- 4. BeeSwarm MQTT适配器 -->
  <node name="beeswarm_mqtt_adapter" 
        pkg="visual_swarm_integration" 
        type="beeswarm_mqtt_adapter_node.py" 
        output="screen">
    <param name="vehicle_id" value="$(arg vehicle_id)"/>
  </node>
  
  <!-- 5. 系统监控 -->
  <node name="visual_swarm_monitor" 
        pkg="visual_swarm_integration" 
        type="system_monitor_node.py" 
        output="screen">
    <param name="vehicle_id" value="$(arg vehicle_id)"/>
    <param name="monitor_frequency" value="1.0"/>
  </node>
  
</launch>
