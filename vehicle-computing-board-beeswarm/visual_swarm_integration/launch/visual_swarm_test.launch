<?xml version="1.0"?>
<launch>
  <!-- VisualSwarm Test Launch File -->
  <!-- Compatible with BeeSwarm's dynamic vehicle registration system -->

  <!-- Parameters -->
  <!-- Note: vehicle_id is automatically determined by hostname (BeeSwarm style) -->
  <!-- You can override it with: roslaunch ... vehicle_id:=custom_name -->
  <arg name="vehicle_id" default="" doc="Vehicle ID (empty = use hostname)"/>

  <!-- Note: Vehicle spawning is handled by BeeSwarm's sim_server.py -->
  <!-- The sim_server automatically assigns positions based on registration order -->
  <!-- Position assignment: -->
  <!-- 1st vehicle: (0.0, 0.0, 0.0) -->
  <!-- 2nd vehicle: (3.0, 0.0, 0.0) -->
  <!-- 3rd vehicle: (-3.0, 0.0, 0.0) -->
  <!-- 4th vehicle: (0.0, -3.0, 0.0) -->
  <!-- 5th vehicle: (0.0, 3.0, 0.0) -->

  <!-- BeeSwarm Registration Node -->
  <!-- This node handles vehicle registration with sim_server.py -->
  <node name="beeswarm_registration"
        pkg="visual_swarm_integration"
        type="beeswarm_registration_node.py"
        output="screen">
    <param name="vehicle_id" value="$(arg vehicle_id)" if="$(eval arg('vehicle_id') != '')"/>
  </node>

  <!-- BeeSwarm Vehicle Control Node -->
  <node name="VelControl"
        pkg="vswarm_sim"
        type="VelControl.py"
        output="screen" />

  <!-- VisualSwarm Main Node -->
  <node name="visual_swarm_main"
        pkg="visual_swarm_integration"
        type="visual_swarm_main_node.py"
        output="screen">
    
    <param name="vehicle_id" value="$(arg vehicle_id)" if="$(eval arg('vehicle_id') != '')"/>
    <param name="GAM" value="0.1"/>
    <param name="V0" value="125"/>
    <param name="ALP0" value="125"/>
    <param name="ALP1" value="0.00075"/>
    <param name="BET0" value="10"/>
    <param name="BET1" value="0.001"/>
    <param name="control_frequency" value="10.0"/>
    <param name="max_linear_velocity" value="2.0"/>
    <param name="max_angular_velocity" value="1.0"/>
  </node>
  
</launch>
