#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VisualSwarm主节点 (简化测试版)
=============================
"""

import rospy
import numpy as np
import socket
import time
import sys
import os
from geometry_msgs.msg import Twist
from nav_msgs.msg import Odometry
from std_msgs.msg import String
from swarm_experiment.msg import DetectionArray

# 添加VisualSwarm模块路径
brain_path = os.path.join(os.path.dirname(__file__), '..', 'visual_swarm_brain')
sys.path.insert(0, brain_path)

# 直接导入VisualSwarm模块，不使用回退机制
from statevar_computation import compute_state_variables, exploration_behavior
from vpf_extraction import VisualSwarmVPFExtractor

class VisualSwarmMainNode:
    """VisualSwarm主节点 (简化版)"""
    
    def __init__(self):
        rospy.init_node('visual_swarm_main', anonymous=True)

        # 使用BeeSwarm的动态命名机制：主机名作为车辆ID
        self.vehicle_id = rospy.get_param('~vehicle_id', socket.gethostname())

        # VisualSwarm算法参数（来自Bastien & Romanczuk 2020论文）
        self.algorithm_params = {
            'GAM': rospy.get_param('~GAM', 0.1),          # 速度衰减系数
            'V0': rospy.get_param('~V0', 125),            # 基础速度
            'ALP0': rospy.get_param('~ALP0', 125),        # 速度响应基础增益
            'ALP1': rospy.get_param('~ALP1', 0.00075),    # 速度响应线性增益
            'BET0': rospy.get_param('~BET0', 10),         # 转向响应基础增益
            'BET1': rospy.get_param('~BET1', 0.001),      # 转向响应线性增益
        }

        # VPF参数
        self.vpf_fov = rospy.get_param('~vpf_fov', 6.28)
        self.vpf_resolution = rospy.get_param('~vpf_resolution', 320)
        self.vpf_image_height = rospy.get_param('~vpf_image_height', 200)

        # 控制参数
        self.max_linear_vel = rospy.get_param('~max_linear_velocity', 2.0)
        self.max_angular_vel = rospy.get_param('~max_angular_velocity', 1.0)
        self.control_frequency = rospy.get_param('~control_frequency', 10.0)
        self.integration_dt = rospy.get_param('~integration_dt', 0.1)
        self.velocity_decay = rospy.get_param('~velocity_decay', 0.95)

        # 🛡️ Lab_106世界边界（与VelControl.py保持一致）
        self.world_bounds = {
            'x_min': -3.0, 'x_max': 3.0,    # 避开左墙(-4.0)和书架(-3.67)
            'y_min': -4.0, 'y_max': 4.0,    # 避开南墙(-5.075)和北墙(+5.0)
        }
        
        # VisualSwarm组件初始化
        self.vpf_extractor = VisualSwarmVPFExtractor(
            fov=self.vpf_fov,
            resolution=self.vpf_resolution,
            image_width=640,
            image_height=self.vpf_image_height
        )
        rospy.loginfo("✅ VPF提取器初始化成功")

        # 状态变量
        self.current_velocity = 0.0
        self.current_angular_velocity = 0.0
        self.detection_count = 0
        self.last_vpf = None
        self.last_phi = None

        # 智能日志控制
        self.last_status_log_time = 0
        
        # 订阅器
        self.detection_sub = rospy.Subscriber('/detection_results', DetectionArray, 
                                            self.detection_callback)
        
        # 发布器 - 使用BeeSwarm的车辆控制话题格式
        vel_cmd_topic = f'/{self.vehicle_id}/vel_cmd'
        self.cmd_vel_pub = rospy.Publisher(vel_cmd_topic, Twist, queue_size=10)
        self.status_pub = rospy.Publisher('/visual_swarm/status', String, queue_size=10)
        
        # 控制定时器
        self.control_timer = rospy.Timer(rospy.Duration(1.0/self.control_frequency), 
                                       self.control_callback)
        
        rospy.loginfo(f"VisualSwarm主节点启动: {self.vehicle_id}")
    
    def detection_callback(self, msg):
        """处理检测结果 - 使用完整VisualSwarm算法"""
        self.detection_count = len(msg.detections)

        if self.detection_count > 0:
            # 使用完整VisualSwarm算法
            Phi, VPF = self.vpf_extractor.detections_to_vpf(msg.detections)
            self.last_phi = Phi
            self.last_vpf = VPF

            # 计算VisualSwarm状态变量
            dvel, dpsi = compute_state_variables(
                vel_now=self.current_velocity,
                Phi=Phi,
                V_now=VPF,
                **self.algorithm_params
            )

            # 积分更新速度和角速度
            self.current_velocity += dvel * self.integration_dt
            self.current_angular_velocity = dpsi

            # 应用速度衰减
            self.current_velocity *= self.velocity_decay

            rospy.loginfo(f"🧠 VisualSwarm算法: 检测{self.detection_count}个目标, dv={dvel:.3f}, dψ={dpsi:.3f}")

        else:
            # 探索行为
            self.current_velocity, self.current_angular_velocity = exploration_behavior(
                self.current_velocity, self.integration_dt
            )
            self.last_vpf = None
            self.last_phi = None
    
    def control_callback(self, event):
        """控制回调 - 适配全向小车"""
        # 限制速度范围
        linear_vel = np.clip(self.current_velocity, 0.0, self.max_linear_vel)
        angular_vel = np.clip(self.current_angular_velocity,
                            -self.max_angular_vel, self.max_angular_vel)

        # 🛡️ 边界感知速度调整（简化版边界避障）
        # 注意：这里我们没有当前位置信息，所以只能做保守的速度限制
        # 真正的边界检查在VelControl.py中进行
        boundary_safety_factor = 0.8  # 在接近边界时降低速度

        # 🚗 全向小车控制策略：
        # 1. 保持前进速度 (linear.x)
        # 2. 将转向转换为侧向移动 (linear.y) - 这是全向车的优势！
        # 3. 减少原地转向 (angular.z)，更多使用平移

        cmd_vel = Twist()
        cmd_vel.linear.x = linear_vel * boundary_safety_factor  # 前进/后退（保守）

        # 将部分转向转换为侧向移动（全向运动的优势）
        lateral_gain = 0.5  # 转向到侧向移动的转换系数
        cmd_vel.linear.y = angular_vel * lateral_gain * boundary_safety_factor  # 左移/右移（保守）

        # 保留部分原地转向能力
        turning_gain = 0.3  # 保留的转向系数
        cmd_vel.angular.z = angular_vel * turning_gain  # 原地转向

        self.cmd_vel_pub.publish(cmd_vel)

        # 调试全向控制
        if abs(angular_vel) > 0.01:
            rospy.logdebug(f"🚗 全向控制: v_x={cmd_vel.linear.x:.3f}, v_y={cmd_vel.linear.y:.3f}, ω={cmd_vel.angular.z:.3f}")
        
        # 发布状态（智能日志：每5秒打印一次状态）
        current_time = time.time()
        if current_time - self.last_status_log_time > 5:
            # 计算VPF统计信息
            vpf_info = ""
            if self.last_vpf is not None:
                vpf_stats = self.vpf_extractor.get_vpf_statistics(self.last_vpf)
                vpf_info = f", VPF占用率={vpf_stats['occupied_ratio']:.2f}, 质心={np.degrees(vpf_stats['center_of_mass']):.1f}°"

            rospy.loginfo(f"🚗 VisualSwarm状态: v={linear_vel:.2f}m/s, ω={angular_vel:.2f}rad/s, 检测数={self.detection_count}{vpf_info}")
            self.last_status_log_time = current_time

        # 发布详细状态信息
        status_msg = String()
        status_data = {
            'vehicle_id': self.vehicle_id,
            'detections': self.detection_count,
            'velocity': round(linear_vel, 3),
            'angular_velocity': round(angular_vel, 3),
            'algorithm': 'VisualSwarm',
            'timestamp': current_time
        }

        if self.last_vpf is not None:
            vpf_stats = self.vpf_extractor.get_vpf_statistics(self.last_vpf)
            status_data.update({
                'vpf_occupied_ratio': round(vpf_stats['occupied_ratio'], 3),
                'vpf_center_of_mass': round(np.degrees(vpf_stats['center_of_mass']), 1)
            })

        status_msg.data = str(status_data)
        self.status_pub.publish(status_msg)
    
    def run(self):
        """运行节点"""
        rospy.loginfo("VisualSwarm主节点开始运行...")
        rospy.spin()

def main():
    try:
        node = VisualSwarmMainNode()
        node.run()
    except rospy.ROSInterruptException:
        rospy.loginfo("VisualSwarm主节点关闭")

if __name__ == '__main__':
    main()
