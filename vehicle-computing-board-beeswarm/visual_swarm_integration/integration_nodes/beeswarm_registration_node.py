#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BeeSwarm车辆注册节点
====================

与BeeSwarm的sim_server.py兼容的车辆注册和状态同步节点。
实现动态车辆注册和位置分配机制。
"""

import rospy
import json
import time
import socket
import threading
import paho.mqtt.client as mqtt
from nav_msgs.msg import Odometry
from gazebo_msgs.srv import SpawnModel, DeleteModel
from geometry_msgs.msg import Pose
import tf

class BeeSwarmRegistrationNode:
    """BeeSwarm车辆注册节点"""
    
    def __init__(self):
        rospy.init_node('beeswarm_registration', anonymous=True)
        
        # 使用BeeSwarm的动态命名机制：主机名作为车辆ID
        self.vehicle_id = rospy.get_param('~vehicle_id', socket.gethostname())
        
        # BeeSwarm MQTT配置（从sim_server.py复制）
        self.broker = '*************'
        self.username = 'b7986fb66280bcee'
        self.password = 'Mx9B2EVhH79BY783Zp6rPcrFHv70kQIqJ2i9B4EbdevZ3M'
        self.port = 1883
        self.keepalive = 60
        
        # MQTT话题
        self.register_topic = '/simserver/register'
        self.recv_topic = '/simserver/recv'
        self.send_topic = '/simserver/send'  # sim_server广播话题
        
        # 车辆状态
        self.is_registered = False
        self.assigned_position = None
        self.current_position = {'x': 0.0, 'y': 0.0, 'z': 0.0}
        self.current_orientation = {'x': 0.0, 'y': 0.0, 'z': 0.0, 'w': 1.0}
        self.led_mode = 0
        
        # 初始化MQTT客户端
        self.client = mqtt.Client(client_id=f"{self.vehicle_id}_car")
        self.client.username_pw_set(self.username, self.password)
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect
        
        # ROS订阅器
        self.odom_sub = rospy.Subscriber('/odom', Odometry, self.odom_callback)
        
        # Gazebo服务
        rospy.wait_for_service('/gazebo/spawn_sdf_model')
        self.spawn_model = rospy.ServiceProxy('/gazebo/spawn_sdf_model', SpawnModel)
        
        # 连接状态
        self.is_connected = False

        # 智能日志控制（参考sim_server策略）
        self.last_broadcast_msg = {}
        self.last_log_time = 0
        
        rospy.loginfo(f"🚗 BeeSwarm注册节点初始化: {self.vehicle_id}")
    
    def connect_and_register(self):
        """连接MQTT并注册车辆"""
        try:
            # 连接MQTT
            self.client.connect(self.broker, self.port, self.keepalive)
            self.client.loop_start()
            
            # 等待连接
            timeout = 10
            start_time = time.time()
            while not self.is_connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if self.is_connected:
                rospy.loginfo("✅ MQTT连接成功")
                
                # 注册车辆
                self.register_vehicle()
                
                # 启动状态发布线程
                self.status_thread = threading.Thread(target=self.status_publish_loop)
                self.status_thread.daemon = True
                self.status_thread.start()
                
                return True
            else:
                rospy.logerr("❌ MQTT连接失败")
                return False
                
        except Exception as e:
            rospy.logerr(f"连接失败: {e}")
            return False
    
    def _on_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            self.is_connected = True
            rospy.loginfo("MQTT连接建立")
            
            # 订阅sim_server广播话题
            self.client.subscribe(self.send_topic)
            rospy.loginfo(f"订阅话题: {self.send_topic}")
        else:
            rospy.logerr(f"MQTT连接失败，返回码: {rc}")
    
    def _on_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        self.is_connected = False
        rospy.logwarn(f"MQTT连接断开，返回码: {rc}")
    
    def _on_message(self, client, userdata, msg):
        """MQTT消息回调"""
        try:
            if msg.topic == self.send_topic:
                # 接收到sim_server的广播消息（包含分配的位置）
                broadcast_msg = json.loads(msg.payload.decode())

                # 智能日志：只在消息变化时打印，或每10秒打印一次（参考sim_server策略）
                current_time = time.time()
                if (broadcast_msg != self.last_broadcast_msg or
                    current_time - self.last_log_time > 10):
                    rospy.loginfo(f"📡 收到广播消息: {broadcast_msg}")
                    self.last_broadcast_msg = broadcast_msg.copy()
                    self.last_log_time = current_time

                if self.vehicle_id in broadcast_msg:
                    vehicle_data = broadcast_msg[self.vehicle_id]

                    # 获取分配的位置
                    assigned_pos = {
                        'x': vehicle_data.get('pos_x', 0.0),
                        'y': vehicle_data.get('pos_y', 0.0),
                        'z': vehicle_data.get('pos_z', 0.0)
                    }

                    # 如果是第一次接收到位置分配，生成车辆
                    if not self.is_registered:
                        self.assigned_position = assigned_pos
                        self.spawn_vehicle_at_position(assigned_pos)
                        self.is_registered = True
                        rospy.loginfo(f"🎯 车辆注册成功，分配位置: {assigned_pos}")

        except Exception as e:
            rospy.logwarn(f"处理MQTT消息失败: {e}")
    
    def register_vehicle(self):
        """向sim_server注册车辆"""
        try:
            register_msg = {
                'agent': self.vehicle_id,
                'timestamp': time.time()
            }
            
            message = json.dumps(register_msg)
            result = self.client.publish(self.register_topic, message)
            
            rospy.loginfo(f"📝 发送注册请求: {self.vehicle_id}")
            
        except Exception as e:
            rospy.logerr(f"注册车辆失败: {e}")
    
    def spawn_vehicle_at_position(self, position):
        """在指定位置生成车辆"""
        try:
            # 读取SDF文件
            sdf_file_path = rospy.get_param('robot_description_file', 
                                          '/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/vswarm_sim/sdf/swarm_car/swarm_car.sdf')
            
            with open(sdf_file_path, 'r') as f:
                sdf_content = f.read()
            
            # 设置初始位置
            initial_pose = Pose()
            initial_pose.position.x = position['x']
            initial_pose.position.y = position['y']
            initial_pose.position.z = position['z']
            initial_pose.orientation.w = 1.0
            
            # 生成车辆
            response = self.spawn_model(
                model_name=self.vehicle_id,
                model_xml=sdf_content,
                robot_namespace='',
                initial_pose=initial_pose,
                reference_frame='world'
            )
            
            if response.success:
                rospy.loginfo(f"🚗 车辆生成成功: {self.vehicle_id} at {position}")
            else:
                rospy.logerr(f"车辆生成失败: {response.status_message}")
                
        except Exception as e:
            rospy.logerr(f"生成车辆失败: {e}")
    
    def odom_callback(self, msg):
        """里程计回调"""
        try:
            # 更新当前位置
            self.current_position = {
                'x': msg.pose.pose.position.x,
                'y': msg.pose.pose.position.y,
                'z': msg.pose.pose.position.z
            }
            
            self.current_orientation = {
                'x': msg.pose.pose.orientation.x,
                'y': msg.pose.pose.orientation.y,
                'z': msg.pose.pose.orientation.z,
                'w': msg.pose.pose.orientation.w
            }
            
        except Exception as e:
            rospy.logwarn(f"处理里程计数据失败: {e}")
    
    def status_publish_loop(self):
        """状态发布循环"""
        rate = rospy.Rate(10)  # 10Hz
        
        while not rospy.is_shutdown() and self.is_connected:
            try:
                if self.is_registered:
                    # 发布状态到sim_server
                    status_msg = {
                        'agent': self.vehicle_id,
                        'data_type': 'transform',
                        'args': {
                            'pos_x': self.current_position['x'],
                            'pos_y': self.current_position['y'],
                            'pos_z': self.current_position['z'],
                            'led_mode': self.led_mode
                        }
                    }
                    
                    message = json.dumps(status_msg)
                    self.client.publish(self.recv_topic, message)
                
                rate.sleep()
                
            except Exception as e:
                rospy.logwarn(f"状态发布失败: {e}")
                break
    
    def run(self):
        """运行节点"""
        if self.connect_and_register():
            rospy.loginfo("🚀 BeeSwarm注册节点开始运行...")
            rospy.spin()
        else:
            rospy.logerr("启动失败")
    
    def shutdown(self):
        """关闭节点"""
        if self.is_connected:
            self.client.loop_stop()
            self.client.disconnect()
        rospy.loginfo("BeeSwarm注册节点关闭")


def main():
    try:
        node = BeeSwarmRegistrationNode()
        node.run()
    except rospy.ROSInterruptException:
        rospy.loginfo("BeeSwarm注册节点关闭")
    except Exception as e:
        rospy.logerr(f"BeeSwarm注册节点出错: {e}")


if __name__ == '__main__':
    main()
