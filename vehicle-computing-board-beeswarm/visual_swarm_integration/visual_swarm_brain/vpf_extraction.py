#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VisualSwarm VPF提取模块
======================

视觉投影场(Visual Projection Field)提取和处理模块。
将检测结果转换为VisualSwarm算法所需的VPF格式。

基于VisualSwarm-develop的vision模块改编。
"""

import numpy as np
import rospy
from swarm_experiment.msg import DetectionArray, DetectionResult


class VisualSwarmVPFExtractor:
    """VisualSwarm VPF提取器"""
    
    def __init__(self, fov=6.28, resolution=320, image_width=640, image_height=480):
        """
        初始化VPF提取器
        
        参数:
            fov (float): 视野范围 (弧度，默认2π全视野)
            resolution (int): VPF分辨率 (默认320)
            image_width (int): 图像宽度 (默认640)
            image_height (int): 图像高度 (默认480)
        """
        self.fov = fov
        self.resolution = resolution
        self.image_width = image_width
        self.image_height = image_height
        
        # 创建视角数组，以车辆前方为0，左侧为正，右侧为负
        self.Phi = np.linspace(-fov/2, fov/2, resolution)
        
        rospy.loginfo(f"🔍 VPF提取器初始化: FOV={fov:.2f}rad, 分辨率={resolution}")
    
    def detections_to_vpf(self, detections):
        """
        将检测结果转换为视觉投影场(VPF)
        
        参数:
            detections: DetectionArray消息中的检测结果列表
        
        返回:
            tuple: (Phi, VPF)
                Phi: 视角数组 (弧度)
                VPF: 视觉投影场数组
        """
        VPF = np.zeros(self.resolution)
        
        if not detections:
            return self.Phi, VPF
        
        for detection in detections:
            try:
                # 计算目标的角度位置
                angle = self._detection_to_angle(detection)
                
                # 计算目标的角度范围
                angular_width = self._detection_to_angular_width(detection)
                
                # 在VPF中标记目标
                self._mark_target_in_vpf(VPF, angle, angular_width)
                
                rospy.logdebug(f"目标映射到VPF: angle={angle:.3f}rad, width={angular_width:.3f}rad")
                
            except Exception as e:
                rospy.logwarn(f"处理检测结果失败: {e}")
                continue
        
        return self.Phi, VPF
    
    def _detection_to_angle(self, detection):
        """
        将检测结果转换为角度

        参数:
            detection: DetectionResult消息

        返回:
            float: 目标角度 (弧度)
        """
        if hasattr(detection, 'x_center'):
            # 使用BeeSwarm DetectionResult消息格式
            center_x = detection.x_center

            # 将像素坐标转换为角度 (-fov/2 到 +fov/2)
            # 图像中心对应前方(0度)，左侧为正，右侧为负
            normalized_x = (center_x / self.image_width) - 0.5
            angle = normalized_x * self.fov

            return angle

        else:
            rospy.logwarn("检测结果缺少位置信息，使用默认角度0")
            return 0.0
    
    def _detection_to_angular_width(self, detection):
        """
        计算检测目标的角度宽度

        参数:
            detection: DetectionResult消息

        返回:
            float: 角度宽度 (弧度)
        """
        if hasattr(detection, 'width') and detection.width > 0:
            # 使用BeeSwarm DetectionResult消息格式
            angular_width = (detection.width / self.image_width) * self.fov

            # 限制最小和最大角度宽度
            min_width = self.fov / 64  # 最小1/64视野
            max_width = self.fov / 8   # 最大1/8视野

            return np.clip(angular_width, min_width, max_width)

        else:
            # 默认角度宽度
            return self.fov / 32  # 约1/32视野
    
    def _mark_target_in_vpf(self, VPF, angle, angular_width):
        """
        在VPF中标记目标
        
        参数:
            VPF: 视觉投影场数组
            angle: 目标角度
            angular_width: 角度宽度
        """
        # 计算目标在VPF中的索引范围
        angle_indices = np.where(
            (self.Phi >= angle - angular_width/2) & 
            (self.Phi <= angle + angular_width/2)
        )[0]
        
        if len(angle_indices) > 0:
            # 标记目标区域
            VPF[angle_indices] = 1.0
    
    def get_vpf_statistics(self, VPF):
        """
        获取VPF统计信息
        
        参数:
            VPF: 视觉投影场数组
        
        返回:
            dict: 统计信息
        """
        stats = {
            'mean_intensity': np.mean(VPF),
            'max_intensity': np.max(VPF),
            'occupied_ratio': np.sum(VPF > 0) / len(VPF),
            'total_targets': np.sum(VPF > 0),
            'center_of_mass': self._calculate_center_of_mass(VPF)
        }
        
        return stats
    
    def _calculate_center_of_mass(self, VPF):
        """
        计算VPF的质心角度
        
        参数:
            VPF: 视觉投影场数组
        
        返回:
            float: 质心角度 (弧度)
        """
        if np.sum(VPF) == 0:
            return 0.0
        
        # 计算加权平均角度
        center_of_mass = np.sum(self.Phi * VPF) / np.sum(VPF)
        return center_of_mass
