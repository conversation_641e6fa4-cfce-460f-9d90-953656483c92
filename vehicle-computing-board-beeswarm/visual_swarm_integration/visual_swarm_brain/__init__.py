#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VisualSwarm Brain模块
===================

VisualSwarm算法的核心"大脑"模块，包含：
- 状态变量计算 (statevar_computation.py)
- VPF提取 (vpf_extraction.py)
- 行为管理 (behavior_manager.py)
"""

__version__ = "1.0.0"
__author__ = "VisualSwarm Integration Team"

# 导入核心模块
try:
    from .statevar_computation import (
        compute_state_variables,
        detections_to_vpf,
        vpf_to_behavior,
        exploration_behavior
    )
    from .vpf_extraction import VisualSwarmVPFExtractor
    
    __all__ = [
        'compute_state_variables',
        'detections_to_vpf', 
        'vpf_to_behavior',
        'exploration_behavior',
        'VisualSwarmVPFExtractor'
    ]
    
except ImportError as e:
    print(f"Warning: Failed to import VisualSwarm brain modules: {e}")
    __all__ = []
