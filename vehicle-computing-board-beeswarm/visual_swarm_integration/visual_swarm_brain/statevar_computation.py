#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VisualSwarm 状态变量计算模块
============================

基于Bastien & Romanczuk (2020)的视觉集群算法数学模型。
论文链接: https://advances.sciencemag.org/content/6/6/eaay0792

实现完整的VisualSwarm核心算法：
- 视觉投影场(VPF)处理
- 状态变量计算（速度变化dv、转向变化dpsi）
- 集群行为的数学模型

作者: 基于VisualSwarm-develop改编
"""

import numpy as np
import numpy.typing as npt
from scipy import integrate
import rospy


def dPhi_V_of(Phi: npt.ArrayLike, V: npt.ArrayLike) -> npt.ArrayLike:
    """
    计算在给定时间点t时VPF相对于Phi视角数组的导数
    
    这是集群算法的核心数学操作之一，用于检测视野中目标的边缘。
    
    参数:
        Phi: 视野轴的线性空间numpy数组
        V: 二进制视觉投影场数组
    
    返回:
        dPhi_V: V相对于Phi的导数数组
    """
    # 为边界情况进行循环填充
    padV = np.pad(V, (1, 1), 'wrap')
    dPhi_V_raw = np.diff(padV)
    
    # 如果边缘有非零值，我们希望包含它
    if dPhi_V_raw[0] > 0 and dPhi_V_raw[-1] > 0:
        dPhi_V_raw = dPhi_V_raw[0:-1]
    else:
        dPhi_V_raw = dPhi_V_raw[1:, ...]
    
    dPhi_V = dPhi_V_raw
    return dPhi_V


def compute_state_variables(vel_now: float, Phi: npt.ArrayLike, V_now: npt.ArrayLike,
                          GAM=0.1, V0=125, ALP0=125, ALP1=0.00075, ALP2=0.0,
                          BET0=10, BET1=0.001, BET2=0.0):
    """
    根据VisualSwarm主算法计算给定智能体的状态变量
    
    实现论文中的核心数学模型：
    https://advances.sciencemag.org/content/6/6/eaay0792
    
    参数:
        vel_now (float): 智能体当前速度
        Phi (npt.ArrayLike): 视野轴的线性空间numpy数组 (弧度)
        V_now (npt.ArrayLike): 当前二进制视觉投影场数组
        
        算法参数:
        GAM (float): 速度衰减系数 (默认: 0.1)
        V0 (float): 基础速度 (默认: 125)
        ALP0 (float): 速度响应基础增益 (默认: 125)
        ALP1 (float): 速度响应线性增益 (默认: 0.00075)
        ALP2 (float): 速度响应二次增益 (默认: 0.0)
        BET0 (float): 转向响应基础增益 (默认: 10)
        BET1 (float): 转向响应线性增益 (默认: 0.001)
        BET2 (float): 转向响应二次增益 (默认: 0.0)
    
    返回:
        tuple: (dvel, dpsi)
            dvel: 智能体速度的时间变化
            dpsi: 智能体航向角的时间变化
    """
    
    # 初始化时间导数为零（当前版本不使用时间导数）
    dt_V = np.zeros(len(Phi))
    
    # 计算VPF相对于Phi的导数
    dPhi_V = dPhi_V_of(Phi, V_now)
    
    # 计算函数G的级数展开
    # 速度相关的G函数
    G_vel = (-V_now + ALP2 * dt_V)
    
    # 尖峰部分需要单独处理，因为数值积分的原因
    G_vel_spike = np.square(dPhi_V)
    
    # 转向相关的G函数
    G_psi = (-V_now + BET2 * dt_V)
    
    # 尖峰部分需要单独处理，因为数值积分的原因
    G_psi_spike = np.square(dPhi_V)
    
    # 计算转向变化dpsi
    # 使用原始算法（不使用S型掩码）
    dpsi = BET0 * integrate.trapz(np.sin(Phi) * G_psi, Phi) + \
           BET0 * BET1 * np.sum(np.sin(Phi) * G_psi_spike)
    
    # 计算速度变化dvel
    # 使用原始算法（不使用S型掩码）
    dvel = GAM * (V0 - vel_now) + \
           ALP0 * integrate.trapz(np.cos(Phi) * G_vel, Phi) + \
           ALP0 * ALP1 * np.sum(np.cos(Phi) * G_vel_spike)
    
    return float(dvel), float(dpsi)


def detections_to_vpf(detections, fov=6.28, resolution=320, image_height=200):
    """
    将检测结果转换为视觉投影场(VPF)
    
    参数:
        detections: 检测结果列表，每个检测包含位置信息
        fov (float): 视野范围 (弧度，默认2π全视野)
        resolution (int): VPF分辨率 (默认320)
        image_height (int): 图像高度 (默认200)
    
    返回:
        tuple: (Phi, VPF)
            Phi: 视角数组 (弧度)
            VPF: 视觉投影场数组
    """
    # 创建视角数组，以车辆前方为0，左侧为正，右侧为负
    Phi = np.linspace(-fov/2, fov/2, resolution)
    VPF = np.zeros(resolution)
    
    if not detections:
        return Phi, VPF
    
    for detection in detections:
        try:
            # 从检测结果获取位置信息
            if hasattr(detection, 'angle'):
                # 如果检测结果直接包含角度信息
                angle = detection.angle
            elif hasattr(detection, 'x') and hasattr(detection, 'width'):
                # 从像素坐标计算角度
                # 假设图像中心对应前方(0度)
                center_x = detection.x + detection.width / 2
                image_width = 640  # 假设图像宽度
                
                # 将像素坐标转换为角度 (-fov/2 到 +fov/2)
                angle = (center_x / image_width - 0.5) * fov
            else:
                rospy.logwarn("检测结果缺少位置信息，跳过")
                continue
            
            # 计算目标在VPF中的影响范围
            if hasattr(detection, 'width'):
                # 根据检测框宽度计算角度范围
                angular_width = (detection.width / 640) * fov  # 假设图像宽度640
            else:
                # 默认角度范围
                angular_width = fov / 32  # 约1/32视野
            
            # 在VPF中标记目标
            angle_indices = np.where(
                (Phi >= angle - angular_width/2) & 
                (Phi <= angle + angular_width/2)
            )[0]
            
            if len(angle_indices) > 0:
                VPF[angle_indices] = 1.0
                
        except Exception as e:
            rospy.logwarn(f"处理检测结果失败: {e}")
            continue
    
    return Phi, VPF


def vpf_to_behavior(Phi, VPF, current_velocity, algorithm_params):
    """
    从VPF计算行为控制命令
    
    参数:
        Phi: 视角数组
        VPF: 视觉投影场
        current_velocity: 当前速度
        algorithm_params: 算法参数字典
    
    返回:
        tuple: (linear_velocity, angular_velocity)
    """
    try:
        # 提取算法参数
        GAM = algorithm_params.get('GAM', 0.1)
        V0 = algorithm_params.get('V0', 125)
        ALP0 = algorithm_params.get('ALP0', 125)
        ALP1 = algorithm_params.get('ALP1', 0.00075)
        BET0 = algorithm_params.get('BET0', 10)
        BET1 = algorithm_params.get('BET1', 0.001)
        
        # 计算状态变量
        dvel, dpsi = compute_state_variables(
            current_velocity, Phi, VPF,
            GAM=GAM, V0=V0, ALP0=ALP0, ALP1=ALP1,
            BET0=BET0, BET1=BET1
        )
        
        # 积分更新速度（简化的欧拉积分）
        dt = 0.1  # 时间步长
        new_velocity = current_velocity + dvel * dt
        angular_velocity = dpsi
        
        # 速度限制
        new_velocity = np.clip(new_velocity, 0.0, 2.0)
        angular_velocity = np.clip(angular_velocity, -1.0, 1.0)
        
        return float(new_velocity), float(angular_velocity)
        
    except Exception as e:
        rospy.logerr(f"VPF行为计算失败: {e}")
        return current_velocity, 0.0


def exploration_behavior(current_velocity, dt=0.1):
    """
    探索行为：当没有检测到目标时的行为
    
    参数:
        current_velocity: 当前速度
        dt: 时间步长
    
    返回:
        tuple: (linear_velocity, angular_velocity)
    """
    # 缓慢加速到巡航速度
    target_velocity = 1.0
    acceleration = 0.5
    
    if current_velocity < target_velocity:
        new_velocity = min(target_velocity, current_velocity + acceleration * dt)
    else:
        new_velocity = current_velocity
    
    # 探索时的随机转向（可选）
    angular_velocity = 0.0
    
    return new_velocity, angular_velocity
