<?xml version="1.0"?>
<launch>
  <!-- VisualSwarm Test Launch File -->
  
  <!-- Parameters -->
  <arg name="vehicle_id" default="vehicle_1" doc="Vehicle ID"/>
  
  <!-- VisualSwarm Parameters -->
  <rosparam>
    visual_swarm:
      GAM: 0.1
      V0: 125
      ALP0: 125
      ALP1: 0.00075
      BET0: 10
      BET1: 0.001
      
      vpf_fov: 6.28
      vpf_resolution: 320
      vpf_image_height: 200
      
      control_frequency: 10.0
      max_linear_velocity: 2.0
      max_angular_velocity: 1.0
      integration_dt: 0.1
      velocity_decay: 0.95
  </rosparam>
  
  <!-- VisualSwarm Main Node -->
  <node name="visual_swarm_main" 
        pkg="visual_swarm_integration" 
        type="visual_swarm_main_node.py" 
        output="screen">
    
    <param name="vehicle_id" value="$(arg vehicle_id)"/>
    
    <param name="GAM" value="0.1"/>
    <param name="V0" value="125"/>
    <param name="ALP0" value="125"/>
    <param name="ALP1" value="0.00075"/>
    <param name="BET0" value="10"/>
    <param name="BET1" value="0.001"/>
    
    <param name="control_frequency" value="10.0"/>
    <param name="max_linear_velocity" value="2.0"/>
    <param name="max_angular_velocity" value="1.0"/>
    <param name="integration_dt" value="0.1"/>
    <param name="velocity_decay" value="0.95"/>
    
    <param name="vpf_fov" value="6.28"/>
    <param name="vpf_resolution" value="320"/>
    <param name="vpf_image_height" value="200"/>
  </node>
  
</launch>
